<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#t-all" data-toggle="tab">{:__('All')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="t-all">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}">
                            <i class="fa fa-refresh"></i> </a>
                        <a href="{:url('statistics')}" class="btn btn-info" title="统计数据">
                            <i class="fa fa-bar-chart"></i> 统计数据 </a>
                        <a href="javascript:;" class="btn btn-warning btn-export" title="导出订单">
                            <i class="fa fa-download"></i> 导出订单 </a>

                        <div class="dropdown btn-group">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown">
                                <i class="fa fa-cog"></i> {:__('More')}
                            </a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-detail btn-disabled disabled" href="javascript:;"><i class="fa fa-list"></i> 查看详情</a></li>
                                <li><a class="btn btn-link btn-refund btn-disabled disabled" href="javascript:;"><i class="fa fa-undo"></i> 退款处理</a></li>
                            </ul>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="statustpl">
    {{# if (d.status == 'paid') { }}
    <span class="label label-success">{{d.status_text}}</span>
    {{# } else if (d.status == 'pending') { }}
    <span class="label label-warning">{{d.status_text}}</span>
    {{# } else if (d.status == 'failed') { }}
    <span class="label label-danger">{{d.status_text}}</span>
    {{# } else if (d.status == 'refunded') { }}
    <span class="label label-default">{{d.status_text}}</span>
    {{# } }}
</script>

<script type="text/html" id="paytypetpl">
    {{# if (d.pay_type == 'wechat') { }}
    <span class="text-success"><i class="fa fa-wechat"></i> {{d.pay_type_text}}</span>
    {{# } else if (d.pay_type == 'alipay') { }}
    <span class="text-primary"><i class="fa fa-alipay"></i> {{d.pay_type_text}}</span>
    {{# } else if (d.pay_type == 'balance') { }}
    <span class="text-warning"><i class="fa fa-money"></i> {{d.pay_type_text}}</span>
    {{# } }}
</script>
