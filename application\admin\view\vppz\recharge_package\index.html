<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#t-all" data-toggle="tab">{:__('All')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="t-all">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}">
                            <i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add" title="{:__('Add')}">
                            <i class="fa fa-plus"></i> {:__('Add')} </a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled" title="{:__('Edit')}">
                            <i class="fa fa-pencil"></i> {:__('Edit')} </a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled" title="{:__('Delete')}">
                            <i class="fa fa-trash"></i> {:__('Delete')} </a>
                        <a href="javascript:;" class="btn btn-danger btn-import" title="{:__('Import')}">
                            <i class="fa fa-upload"></i> {:__('Import')} </a>

                        <div class="dropdown btn-group">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown">
                                <i class="fa fa-cog"></i> {:__('More')}
                            </a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="statustpl">
    {{# if (d.status == 'normal') { }}
    <span class="text-success">{{d.status_text}}</span>
    {{# } else { }}
    <span class="text-muted">{{d.status_text}}</span>
    {{# } }}
</script>

<script type="text/html" id="ishottpl">
    {{# if (d.is_hot == 1) { }}
    <span class="label label-danger">热门</span>
    {{# } else { }}
    <span class="label label-default">普通</span>
    {{# } }}
</script>
                </div>
            </div>
        </div>
    </div>
</div>
