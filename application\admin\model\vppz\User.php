<?php

namespace app\admin\model\vppz;

use think\Model;


class User extends Model
{

    

    

    // 表名
    protected $name = 'vppz_user';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
		'avatar_url',	// 需在此处追加属性，才会触发获取器 getAvatarUrlAttr
		'balance_text'
    ];
    

    







    public function area()
    {
        return $this->belongsTo('Area', 'area_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

	// 获取器直接转换url
	public function getAvatarUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['avatar']);
    }

    // 余额文本获取器
    public function getBalanceTextAttr($value, $data)
    {
        $balance = isset($data['balance']) ? $data['balance'] : 0;
        return '￥' . number_format($balance, 2);
    }

    /**
     * 增加用户余额
     * @param int $userId 用户ID
     * @param float $amount 金额
     * @param string $memo 备注
     * @param string $biz 业务类型
     * @param int $bizId 业务ID
     * @return bool
     */
    public static function addBalance($userId, $amount, $memo = '', $biz = 'recharge', $bizId = 0)
    {
        $user = self::get($userId);
        if (!$user) {
            return false;
        }

        \think\Db::startTrans();
        try {
            $beforeBalance = $user['balance'];
            $afterBalance = $beforeBalance + $amount;

            // 更新用户余额
            $user->save(['balance' => $afterBalance]);

            // 记录资金流水
            \app\admin\model\vppz\Money::create([
                'app_id' => $user['app_id'],
                'area_id' => $user['area_id'],
                'user_id' => $userId,
                'biz' => $biz,
                'biz_id' => $bizId,
                'money' => $amount,
                'before' => $beforeBalance,
                'after' => $afterBalance,
                'memo' => $memo,
                'createtime' => time(),
                'updatetime' => time()
            ]);

            \think\Db::commit();
            return true;
        } catch (\Exception $e) {
            \think\Db::rollback();
            return false;
        }
    }

    /**
     * 扣减用户余额
     * @param int $userId 用户ID
     * @param float $amount 金额
     * @param string $memo 备注
     * @param string $biz 业务类型
     * @param int $bizId 业务ID
     * @return bool
     */
    public static function deductBalance($userId, $amount, $memo = '', $biz = 'order', $bizId = 0)
    {
        $user = self::get($userId);
        if (!$user || $user['balance'] < $amount) {
            return false;
        }

        \think\Db::startTrans();
        try {
            $beforeBalance = $user['balance'];
            $afterBalance = $beforeBalance - $amount;

            // 更新用户余额
            $user->save(['balance' => $afterBalance]);

            // 记录资金流水
            \app\admin\model\vppz\Money::create([
                'app_id' => $user['app_id'],
                'area_id' => $user['area_id'],
                'user_id' => $userId,
                'biz' => $biz,
                'biz_id' => $bizId,
                'money' => -$amount,
                'before' => $beforeBalance,
                'after' => $afterBalance,
                'memo' => $memo,
                'createtime' => time(),
                'updatetime' => time()
            ]);

            \think\Db::commit();
            return true;
        } catch (\Exception $e) {
            \think\Db::rollback();
            return false;
        }
    }

    /**
     * 检查用户余额是否充足
     * @param int $userId 用户ID
     * @param float $amount 金额
     * @return bool
     */
    public static function checkBalance($userId, $amount)
    {
        $user = self::get($userId);
        return $user && $user['balance'] >= $amount;
    }
}
