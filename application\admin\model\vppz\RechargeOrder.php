<?php

namespace app\admin\model\vppz;

use think\Model;
use traits\model\SoftDelete;

class RechargeOrder extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'vppz_recharge_order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text',
        'pay_type_text',
        'pay_time_text'
    ];

    /**
     * 订单状态列表
     */
    public function getStatusList()
    {
        return [
            'pending' => __('Pending'),
            'paid' => __('Paid'),
            'failed' => __('Failed'),
            'refunded' => __('Refunded')
        ];
    }

    /**
     * 支付方式列表
     */
    public function getPayTypeList()
    {
        return [
            'wechat' => __('WeChat'),
            'alipay' => __('Alipay'),
            'balance' => __('Balance')
        ];
    }

    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 支付方式文本获取器
     */
    public function getPayTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['pay_type']) ? $data['pay_type'] : '');
        $list = $this->getPayTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 支付时间文本获取器
     */
    public function getPayTimeTextAttr($value, $data)
    {
        $payTime = isset($data['pay_time']) ? $data['pay_time'] : 0;
        return $payTime > 0 ? date('Y-m-d H:i:s', $payTime) : '';
    }

    /**
     * 生成订单号
     * @return string
     */
    public static function generateOrderNo()
    {
        return 'RCH' . date('YmdHis') . rand(1000, 9999);
    }

    /**
     * 创建充值订单
     * @param array $data 订单数据
     * @return RechargeOrder|false
     */
    public static function createOrder($data)
    {
        $order = new self();
        
        // 设置默认值
        $data['order_no'] = self::generateOrderNo();
        $data['status'] = 'pending';
        $data['createtime'] = time();
        $data['updatetime'] = time();
        
        // 计算实际到账金额
        $data['total_amount'] = $data['amount'] + ($data['reward_amount'] ?? 0);
        
        return $order->save($data) ? $order : false;
    }

    /**
     * 支付成功处理
     * @param string $orderNo 订单号
     * @param string $tradeNo 第三方交易号
     * @param array $payParams 支付参数
     * @return bool
     */
    public static function paySuccess($orderNo, $tradeNo = '', $payParams = [])
    {
        $order = self::where('order_no', $orderNo)->find();
        if (!$order || $order['status'] != 'pending') {
            return false;
        }

        \think\Db::startTrans();
        try {
            // 更新订单状态
            $order->save([
                'status' => 'paid',
                'trade_no' => $tradeNo,
                'pay_time' => time(),
                'pay_params' => json_encode($payParams),
                'updatetime' => time()
            ]);

            // 更新用户余额
            $user = \app\admin\model\vppz\User::get($order['user_id']);
            if ($user) {
                $user->save([
                    'balance' => $user['balance'] + $order['total_amount'],
                    'total_recharge' => $user['total_recharge'] + $order['amount'],
                    'total_reward' => $user['total_reward'] + $order['reward_amount']
                ]);

                // 记录资金流水
                \app\admin\model\vppz\Money::create([
                    'app_id' => $order['app_id'],
                    'area_id' => $order['area_id'],
                    'user_id' => $order['user_id'],
                    'biz' => 'recharge',
                    'biz_id' => $order['id'],
                    'money' => $order['total_amount'],
                    'before' => $user['balance'],
                    'after' => $user['balance'] + $order['total_amount'],
                    'memo' => '充值到账：' . $order['amount'] . '元，奖励：' . $order['reward_amount'] . '元',
                    'createtime' => time(),
                    'updatetime' => time()
                ]);
            }

            \think\Db::commit();
            return true;
        } catch (\Exception $e) {
            \think\Db::rollback();
            return false;
        }
    }

    /**
     * 支付失败处理
     * @param string $orderNo 订单号
     * @param string $reason 失败原因
     * @return bool
     */
    public static function payFailed($orderNo, $reason = '')
    {
        $order = self::where('order_no', $orderNo)->find();
        if (!$order || $order['status'] != 'pending') {
            return false;
        }

        return $order->save([
            'status' => 'failed',
            'remark' => $reason,
            'updatetime' => time()
        ]);
    }

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 关联充值套餐模型
     */
    public function package()
    {
        return $this->belongsTo('RechargePackage', 'package_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 关联应用模型
     */
    public function app()
    {
        return $this->belongsTo('App', 'app_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 关联区域模型
     */
    public function area()
    {
        return $this->belongsTo('Area', 'area_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 获取订单统计数据
     * @param array $where 查询条件
     * @return array
     */
    public static function getStatistics($where = [])
    {
        $statistics = [
            'total_count' => 0,
            'total_amount' => 0,
            'paid_count' => 0,
            'paid_amount' => 0,
            'pending_count' => 0,
            'failed_count' => 0
        ];

        $query = self::where($where);
        
        $statistics['total_count'] = $query->count();
        $statistics['total_amount'] = $query->sum('amount');
        
        $statistics['paid_count'] = $query->where('status', 'paid')->count();
        $statistics['paid_amount'] = $query->where('status', 'paid')->sum('amount');
        
        $statistics['pending_count'] = $query->where('status', 'pending')->count();
        $statistics['failed_count'] = $query->where('status', 'failed')->count();

        return $statistics;
    }
}
