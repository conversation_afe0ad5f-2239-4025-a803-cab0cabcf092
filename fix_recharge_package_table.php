<?php
// 修复充值套餐表结构
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>修复充值套餐表结构</h2>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";

try {
    // 数据库连接配置
    $dsn = "mysql:host=127.0.0.1;port=3306;dbname=fast;charset=utf8mb4";
    $username = "root";
    $password = "tHucJ7ggCMFFC72P";
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✓ 数据库连接成功</div>\n";
    
    // 检查当前表结构
    echo "<h3>当前表结构</h3>\n";
    $stmt = $pdo->query("DESCRIBE fa_vppz_recharge_package");
    $columns = $stmt->fetchAll();
    $existingFields = array_column($columns, 'Field');
    
    echo "<div class='info'>现有字段: " . implode(', ', $existingFields) . "</div>\n";
    
    // 需要添加的字段
    $fieldsToAdd = [
        'app_id' => "ADD COLUMN app_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '应用ID' AFTER id",
        'area_id' => "ADD COLUMN area_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '区域ID' AFTER app_id",
        'start_time' => "ADD COLUMN start_time int(10) unsigned NOT NULL DEFAULT 0 COMMENT '开始时间' AFTER sort",
        'end_time' => "ADD COLUMN end_time int(10) unsigned NOT NULL DEFAULT 0 COMMENT '结束时间' AFTER start_time"
    ];
    
    echo "<h3>添加缺失字段</h3>\n";
    
    foreach ($fieldsToAdd as $field => $sql) {
        if (!in_array($field, $existingFields)) {
            try {
                $pdo->exec("ALTER TABLE fa_vppz_recharge_package {$sql}");
                echo "<div class='success'>✓ 已添加字段: {$field}</div>\n";
            } catch (Exception $e) {
                echo "<div class='error'>✗ 添加字段 {$field} 失败: " . $e->getMessage() . "</div>\n";
            }
        } else {
            echo "<div class='info'>- 字段 {$field} 已存在</div>\n";
        }
    }
    
    // 检查并修复充值订单表
    echo "<h3>检查充值订单表</h3>\n";
    
    $stmt = $pdo->query("DESCRIBE fa_vppz_recharge_order");
    $orderColumns = $stmt->fetchAll();
    $orderFields = array_column($orderColumns, 'Field');
    
    $orderFieldsToAdd = [
        'app_id' => "ADD COLUMN app_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '应用ID' AFTER id"
    ];
    
    foreach ($orderFieldsToAdd as $field => $sql) {
        if (!in_array($field, $orderFields)) {
            try {
                $pdo->exec("ALTER TABLE fa_vppz_recharge_order {$sql}");
                echo "<div class='success'>✓ 已添加订单表字段: {$field}</div>\n";
            } catch (Exception $e) {
                echo "<div class='error'>✗ 添加订单表字段 {$field} 失败: " . $e->getMessage() . "</div>\n";
            }
        } else {
            echo "<div class='info'>- 订单表字段 {$field} 已存在</div>\n";
        }
    }
    
    // 更新现有数据的app_id
    echo "<h3>更新现有数据</h3>\n";
    
    try {
        // 为现有套餐数据设置默认app_id
        $stmt = $pdo->prepare("UPDATE fa_vppz_recharge_package SET app_id = 1 WHERE app_id = 0");
        $affected = $stmt->execute();
        echo "<div class='success'>✓ 已更新套餐数据的app_id</div>\n";
        
        // 为现有订单数据设置默认app_id
        $stmt = $pdo->prepare("UPDATE fa_vppz_recharge_order SET app_id = 1 WHERE app_id = 0");
        $affected = $stmt->execute();
        echo "<div class='success'>✓ 已更新订单数据的app_id</div>\n";
        
    } catch (Exception $e) {
        echo "<div class='error'>✗ 更新数据失败: " . $e->getMessage() . "</div>\n";
    }
    
    // 检查最终表结构
    echo "<h3>最终表结构</h3>\n";
    $stmt = $pdo->query("DESCRIBE fa_vppz_recharge_package");
    $finalColumns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse:collapse; margin:10px 0; width:100%;'>\n";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>默认值</th><th>注释</th></tr>\n";
    foreach ($finalColumns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>" . (isset($column['Comment']) ? $column['Comment'] : '') . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<div class='success'>✓ 表结构修复完成！</div>\n";
    echo "<div class='info'>现在可以重新访问后台充值套餐管理页面了。</div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>修复失败: " . $e->getMessage() . "</div>\n";
}

echo "<br><p><em>修复时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
