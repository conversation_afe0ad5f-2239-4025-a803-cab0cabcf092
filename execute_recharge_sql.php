<?php
// 执行充值功能数据库脚本
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>执行充值功能数据库脚本</h2>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";

try {
    // 数据库连接配置
    $dsn = "mysql:host=127.0.0.1;port=3306;dbname=fast;charset=utf8mb4";
    $username = "root";
    $password = "tHucJ7ggCMFFC72P";
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✓ 数据库连接成功</div>\n";
    
    // 读取SQL脚本
    $sqlFile = 'recharge_database.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL脚本文件不存在: {$sqlFile}");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "<div class='info'>✓ SQL脚本读取成功</div>\n";
    
    // 分割SQL语句（以分号分割，但要处理存储过程等特殊情况）
    $statements = [];
    $current = '';
    $inDelimiter = false;
    
    $lines = explode("\n", $sql);
    foreach ($lines as $line) {
        $line = trim($line);
        
        // 跳过注释和空行
        if (empty($line) || substr($line, 0, 2) === '--') {
            continue;
        }
        
        // 处理DELIMITER
        if (stripos($line, 'DELIMITER') === 0) {
            $inDelimiter = !$inDelimiter;
            continue;
        }
        
        $current .= $line . "\n";
        
        // 如果不在DELIMITER块中，且以分号结尾，则为一个完整语句
        if (!$inDelimiter && substr($line, -1) === ';') {
            $statements[] = trim($current);
            $current = '';
        }
    }
    
    // 添加最后一个语句（如果有）
    if (!empty(trim($current))) {
        $statements[] = trim($current);
    }
    
    echo "<div class='info'>✓ 解析到 " . count($statements) . " 条SQL语句</div>\n";
    
    // 执行SQL语句
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $index => $statement) {
        if (empty(trim($statement))) continue;
        
        try {
            $result = $pdo->exec($statement);
            $successCount++;
            
            // 如果是SELECT语句，获取结果
            if (stripos(trim($statement), 'SELECT') === 0) {
                $stmt = $pdo->query($statement);
                $row = $stmt->fetch();
                if ($row) {
                    echo "<div class='info'>语句 " . ($index + 1) . ": " . (isset($row['message']) ? $row['message'] : (isset($row['result']) ? $row['result'] : '执行成功')) . "</div>\n";
                }
            } else {
                echo "<div class='success'>语句 " . ($index + 1) . ": 执行成功</div>\n";
            }
            
        } catch (PDOException $e) {
            $errorCount++;
            echo "<div class='error'>语句 " . ($index + 1) . " 执行失败: " . $e->getMessage() . "</div>\n";
            echo "<div class='error'>SQL: " . substr($statement, 0, 100) . "...</div>\n";
        }
    }
    
    echo "<h3>执行结果统计</h3>\n";
    echo "<div class='success'>成功: {$successCount} 条</div>\n";
    echo "<div class='error'>失败: {$errorCount} 条</div>\n";
    
    // 验证表是否创建成功
    echo "<h3>验证表结构</h3>\n";
    
    $tables = [
        'fa_vppz_recharge_package' => '充值套餐表',
        'fa_vppz_recharge_order' => '充值订单表'
    ];
    
    foreach ($tables as $table => $desc) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='success'>✓ {$desc} ({$table}) 创建成功</div>\n";
                
                // 检查记录数
                $countStmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
                $count = $countStmt->fetch();
                echo "<div class='info'>  - 当前记录数: {$count['count']}</div>\n";
            } else {
                echo "<div class='error'>✗ {$desc} ({$table}) 创建失败</div>\n";
            }
        } catch (Exception $e) {
            echo "<div class='error'>✗ 检查 {$desc} 时出错: " . $e->getMessage() . "</div>\n";
        }
    }
    
    // 验证用户表字段
    echo "<h3>验证用户表扩展字段</h3>\n";
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM fa_vppz_user LIKE 'balance'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✓ 用户表余额字段添加成功</div>\n";
        } else {
            echo "<div class='error'>✗ 用户表余额字段添加失败</div>\n";
        }
        
        $stmt = $pdo->query("SHOW COLUMNS FROM fa_vppz_user LIKE 'total_recharge'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✓ 用户表累计充值字段添加成功</div>\n";
        } else {
            echo "<div class='error'>✗ 用户表累计充值字段添加失败</div>\n";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ 验证用户表字段时出错: " . $e->getMessage() . "</div>\n";
    }
    
    echo "<h3>数据库脚本执行完成！</h3>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>执行失败: " . $e->getMessage() . "</div>\n";
}

echo "<br><p><em>执行时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
