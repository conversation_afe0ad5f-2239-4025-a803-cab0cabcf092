<?php
// 测试API响应格式
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>测试充值套餐API响应格式</h2>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border:1px solid #ddd;}</style>\n";

// 模拟AJAX请求测试API
$testUrl = "http://127.0.0.1:888/admin/vppz/recharge_package/index";

// 构建请求参数
$params = [
    'sort' => 'sort',
    'order' => 'desc',
    'offset' => 0,
    'limit' => 10,
    'filter' => '{}',
    'op' => '{}',
    '_' => time() * 1000
];

$queryString = http_build_query($params);
$fullUrl = $testUrl . '?' . $queryString;

echo "<div class='info'><strong>测试URL:</strong> {$fullUrl}</div>\n";

// 设置请求头模拟AJAX请求
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => [
            'X-Requested-With: XMLHttpRequest',
            'Accept: application/json, text/javascript, */*; q=0.01',
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]
]);

echo "<h3>1. 发送AJAX请求</h3>\n";

try {
    $response = file_get_contents($fullUrl, false, $context);
    
    if ($response === false) {
        echo "<div class='error'>✗ 请求失败</div>\n";
    } else {
        echo "<div class='success'>✓ 请求成功</div>\n";
        
        // 检查响应是否为JSON格式
        $jsonData = json_decode($response, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<div class='success'>✓ 响应为有效JSON格式</div>\n";
            
            // 检查必要的字段
            if (isset($jsonData['total']) && isset($jsonData['rows'])) {
                echo "<div class='success'>✓ 包含必要字段 (total, rows)</div>\n";
                echo "<div class='info'>总记录数: {$jsonData['total']}</div>\n";
                echo "<div class='info'>返回行数: " . count($jsonData['rows']) . "</div>\n";
                
                // 显示第一条记录的结构
                if (!empty($jsonData['rows'])) {
                    echo "<h3>2. 第一条记录结构</h3>\n";
                    $firstRow = $jsonData['rows'][0];
                    echo "<pre>" . json_encode($firstRow, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";
                    
                    // 检查必要字段
                    $requiredFields = ['id', 'title', 'amount', 'reward_amount', 'status', 'status_text'];
                    $missingFields = [];
                    foreach ($requiredFields as $field) {
                        if (!isset($firstRow[$field])) {
                            $missingFields[] = $field;
                        }
                    }
                    
                    if (empty($missingFields)) {
                        echo "<div class='success'>✓ 包含所有必要字段</div>\n";
                    } else {
                        echo "<div class='error'>✗ 缺少字段: " . implode(', ', $missingFields) . "</div>\n";
                    }
                } else {
                    echo "<div class='error'>✗ 没有返回数据行</div>\n";
                }
            } else {
                echo "<div class='error'>✗ 缺少必要字段 (total 或 rows)</div>\n";
            }
            
            echo "<h3>3. 完整响应数据</h3>\n";
            echo "<pre>" . json_encode($jsonData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";
            
        } else {
            echo "<div class='error'>✗ 响应不是有效的JSON格式</div>\n";
            echo "<div class='error'>JSON错误: " . json_last_error_msg() . "</div>\n";
            echo "<h3>原始响应内容:</h3>\n";
            echo "<pre>" . htmlspecialchars($response) . "</pre>\n";
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>✗ 请求异常: " . $e->getMessage() . "</div>\n";
}

// 测试充值订单API
echo "<hr><h2>测试充值订单API响应格式</h2>\n";

$orderTestUrl = "http://127.0.0.1:888/admin/vppz/recharge_order/index";
$orderFullUrl = $orderTestUrl . '?' . $queryString;

echo "<div class='info'><strong>测试URL:</strong> {$orderFullUrl}</div>\n";

try {
    $orderResponse = file_get_contents($orderFullUrl, false, $context);
    
    if ($orderResponse === false) {
        echo "<div class='error'>✗ 订单API请求失败</div>\n";
    } else {
        echo "<div class='success'>✓ 订单API请求成功</div>\n";
        
        $orderJsonData = json_decode($orderResponse, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<div class='success'>✓ 订单API响应为有效JSON格式</div>\n";
            echo "<div class='info'>订单总记录数: " . (isset($orderJsonData['total']) ? $orderJsonData['total'] : '未知') . "</div>\n";
            echo "<div class='info'>订单返回行数: " . (isset($orderJsonData['rows']) ? count($orderJsonData['rows']) : '未知') . "</div>\n";
        } else {
            echo "<div class='error'>✗ 订单API响应不是有效的JSON格式</div>\n";
            echo "<div class='error'>JSON错误: " . json_last_error_msg() . "</div>\n";
        }
    }
} catch (Exception $e) {
    echo "<div class='error'>✗ 订单API请求异常: " . $e->getMessage() . "</div>\n";
}

echo "<h3>4. 测试建议</h3>\n";
echo "<div class='info'>\n";
echo "<strong>如果仍然出现问题，请检查:</strong><br>\n";
echo "1. 确保数据库中有测试数据<br>\n";
echo "2. 检查控制器中的错误处理<br>\n";
echo "3. 查看FastAdmin的错误日志<br>\n";
echo "4. 确保模型类正确加载<br>\n";
echo "5. 检查权限配置是否正确<br>\n";
echo "</div>\n";

echo "<div class='info'>\n";
echo "<strong>后台访问链接:</strong><br>\n";
echo "<a href='http://127.0.0.1:888/admin' target='_blank'>后台登录</a><br>\n";
echo "<a href='http://127.0.0.1:888/admin/vppz/recharge_package' target='_blank'>充值套餐管理</a><br>\n";
echo "<a href='http://127.0.0.1:888/admin/vppz/recharge_order' target='_blank'>充值订单管理</a><br>\n";
echo "</div>\n";

echo "<br><p><em>测试时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
