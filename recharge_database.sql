-- 嘀嗒陪护系统 - 会员充值功能数据库脚本
-- 执行时间: 2025-07-31
-- 开发者: FastAdmin开发工程师

-- =====================================================
-- 1. 扩展用户表，添加余额相关字段
-- =====================================================

-- 检查字段是否已存在，避免重复添加
SET @sql = '';
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'fa_vppz_user' 
  AND COLUMN_NAME = 'balance';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE `fa_vppz_user` 
     ADD COLUMN `balance` decimal(12,2) DEFAULT ''0.00'' COMMENT ''账户余额'' AFTER `expends`,
     ADD COLUMN `total_recharge` decimal(12,2) DEFAULT ''0.00'' COMMENT ''累计充值金额'' AFTER `balance`,
     ADD COLUMN `total_reward` decimal(12,2) DEFAULT ''0.00'' COMMENT ''累计充值奖励'' AFTER `total_recharge`;',
    'SELECT ''用户表余额字段已存在'' as message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 2. 创建充值套餐表
-- =====================================================

CREATE TABLE IF NOT EXISTS `fa_vppz_recharge_package` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_id` bigint(20) DEFAULT '0' COMMENT '应用ID',
  `area_id` bigint(20) DEFAULT '0' COMMENT '运营区域ID',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '套餐名称',
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '充值金额',
  `reward_amount` decimal(12,2) DEFAULT '0.00' COMMENT '奖励金额',
  `reward_type` enum('fixed','percent') DEFAULT 'fixed' COMMENT '奖励类型:fixed=固定金额,percent=百分比',
  `reward_value` decimal(8,2) DEFAULT '0.00' COMMENT '奖励值',
  `description` text COMMENT '套餐描述',
  `icon` varchar(255) DEFAULT '' COMMENT '套餐图标',
  `sort` int(10) DEFAULT '0' COMMENT '排序权重',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态',
  `is_hot` tinyint(1) DEFAULT '0' COMMENT '是否热门推荐',
  `start_time` bigint(20) DEFAULT '0' COMMENT '生效开始时间',
  `end_time` bigint(20) DEFAULT '0' COMMENT '生效结束时间',
  `createtime` bigint(20) DEFAULT '0' COMMENT '创建时间',
  `updatetime` bigint(20) DEFAULT '0' COMMENT '更新时间',
  `deletetime` bigint(20) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `app_area` (`app_id`,`area_id`),
  KEY `status_sort` (`status`,`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值套餐表';

-- =====================================================
-- 3. 创建充值订单表
-- =====================================================

CREATE TABLE IF NOT EXISTS `fa_vppz_recharge_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_id` bigint(20) DEFAULT '0' COMMENT '应用ID',
  `area_id` bigint(20) DEFAULT '0' COMMENT '运营区域ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `package_id` bigint(20) DEFAULT '0' COMMENT '套餐ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `trade_no` varchar(64) DEFAULT '' COMMENT '第三方交易号',
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '充值金额',
  `reward_amount` decimal(12,2) DEFAULT '0.00' COMMENT '奖励金额',
  `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '实际到账金额',
  `pay_type` enum('wechat','alipay','balance') DEFAULT 'wechat' COMMENT '支付方式',
  `status` enum('pending','paid','failed','refunded') DEFAULT 'pending' COMMENT '订单状态',
  `pay_time` bigint(20) DEFAULT '0' COMMENT '支付时间',
  `pay_params` text COMMENT '支付回调参数',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `createtime` bigint(20) DEFAULT '0' COMMENT '创建时间',
  `updatetime` bigint(20) DEFAULT '0' COMMENT '更新时间',
  `deletetime` bigint(20) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`),
  KEY `status_time` (`status`,`createtime`),
  KEY `app_area` (`app_id`,`area_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值订单表';

-- =====================================================
-- 4. 扩展资金流水表业务类型
-- =====================================================

-- 检查是否需要修改biz字段的枚举值
SET @sql = '';
SELECT COLUMN_TYPE INTO @col_type 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'fa_vppz_money' 
  AND COLUMN_NAME = 'biz';

SET @sql = IF(@col_type NOT LIKE '%recharge%', 
    'ALTER TABLE `fa_vppz_money` 
     MODIFY COLUMN `biz` enum(''order'',''outcash'',''settle'',''recharge'') CHARACTER SET utf8mb4 DEFAULT NULL 
     COMMENT ''业务:order=交易,outcash=提现,settle=结算,recharge=充值'';',
    'SELECT ''资金流水表业务类型已包含充值'' as message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 5. 插入默认充值套餐数据
-- =====================================================

INSERT IGNORE INTO `fa_vppz_recharge_package` 
(`id`, `app_id`, `area_id`, `title`, `amount`, `reward_amount`, `reward_type`, `reward_value`, `description`, `sort`, `status`, `is_hot`, `createtime`, `updatetime`) 
VALUES 
(1, 0, 0, '充值50元', 50.00, 5.00, 'fixed', 5.00, '充值50元送5元，性价比超高！', 100, 'normal', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 0, 0, '充值100元', 100.00, 15.00, 'fixed', 15.00, '充值100元送15元，更加优惠！', 200, 'normal', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 0, 0, '充值200元', 200.00, 40.00, 'fixed', 40.00, '充值200元送40元，超值推荐！', 300, 'normal', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, 0, 0, '充值500元', 500.00, 120.00, 'fixed', 120.00, '充值500元送120元，VIP专享！', 400, 'normal', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- =====================================================
-- 6. 创建充值相关索引优化
-- =====================================================

-- 为用户表余额字段添加索引
ALTER TABLE `fa_vppz_user` ADD INDEX `idx_balance` (`balance`);

-- 为充值订单表添加复合索引
ALTER TABLE `fa_vppz_recharge_order` ADD INDEX `idx_user_status` (`user_id`, `status`);
ALTER TABLE `fa_vppz_recharge_order` ADD INDEX `idx_pay_time` (`pay_time`);

-- 为充值套餐表添加复合索引
ALTER TABLE `fa_vppz_recharge_package` ADD INDEX `idx_status_hot_sort` (`status`, `is_hot`, `sort`);

-- =====================================================
-- 数据库脚本执行完成
-- =====================================================

SELECT '充值功能数据库结构创建完成！' as result;
