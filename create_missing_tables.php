<?php
// 创建缺失的数据库表
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>创建缺失的充值系统数据库表</h2>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .warning{color:orange;} pre{background:#f5f5f5;padding:10px;border:1px solid #ddd;}</style>\n";

try {
    // 数据库连接
    $dsn = "mysql:host=127.0.0.1;port=3306;dbname=fast;charset=utf8mb4";
    $username = "root";
    $password = "tHucJ7ggCMFFC72P";
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✓ 数据库连接成功</div>\n";
    
    // 1. 检查现有表
    echo "<h3>1. 检查现有表</h3>\n";
    
    $tables = [
        'fa_vppz_recharge_package' => '充值套餐表',
        'fa_vppz_recharge_order' => '充值订单表'
    ];
    
    foreach ($tables as $table => $desc) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        $exists = $stmt->fetch();
        
        if ($exists) {
            echo "<div class='info'>- {$desc} 已存在</div>\n";
        } else {
            echo "<div class='warning'>- {$desc} 不存在，需要创建</div>\n";
        }
    }
    
    // 2. 创建充值套餐表
    echo "<h3>2. 创建充值套餐表</h3>\n";
    
    $createPackageTable = "
    CREATE TABLE IF NOT EXISTS `fa_vppz_recharge_package` (
      `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
      `app_id` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '应用ID',
      `area_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '区域ID',
      `title` varchar(100) NOT NULL DEFAULT '' COMMENT '套餐名称',
      `description` varchar(255) NOT NULL DEFAULT '' COMMENT '套餐描述',
      `amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '充值金额',
      `reward_type` enum('fixed','percent') NOT NULL DEFAULT 'fixed' COMMENT '奖励类型:fixed=固定金额,percent=百分比',
      `reward_value` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '奖励值',
      `reward_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '奖励金额',
      `icon` varchar(255) NOT NULL DEFAULT '' COMMENT '套餐图标',
      `sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
      `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
      `is_hot` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否热门:0=否,1=是',
      `start_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '开始时间',
      `end_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
      `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
      `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
      `deletetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
      PRIMARY KEY (`id`),
      KEY `app_id` (`app_id`),
      KEY `area_id` (`area_id`),
      KEY `status` (`status`),
      KEY `sort` (`sort`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值套餐表';
    ";
    
    try {
        $pdo->exec($createPackageTable);
        echo "<div class='success'>✓ 充值套餐表创建成功</div>\n";
    } catch (Exception $e) {
        echo "<div class='error'>✗ 充值套餐表创建失败: " . $e->getMessage() . "</div>\n";
    }
    
    // 3. 创建充值订单表
    echo "<h3>3. 创建充值订单表</h3>\n";
    
    $createOrderTable = "
    CREATE TABLE IF NOT EXISTS `fa_vppz_recharge_order` (
      `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
      `app_id` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '应用ID',
      `area_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '区域ID',
      `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
      `package_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '套餐ID',
      `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
      `transaction_id` varchar(100) NOT NULL DEFAULT '' COMMENT '交易流水号',
      `amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '充值金额',
      `reward_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '奖励金额',
      `total_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '总金额',
      `pay_type` varchar(20) NOT NULL DEFAULT '' COMMENT '支付方式',
      `status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending' COMMENT '状态',
      `pay_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '支付时间',
      `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
      `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
      `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
      `deletetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
      PRIMARY KEY (`id`),
      UNIQUE KEY `order_no` (`order_no`),
      KEY `app_id` (`app_id`),
      KEY `area_id` (`area_id`),
      KEY `user_id` (`user_id`),
      KEY `package_id` (`package_id`),
      KEY `status` (`status`),
      KEY `pay_type` (`pay_type`),
      KEY `createtime` (`createtime`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值订单表';
    ";
    
    try {
        $pdo->exec($createOrderTable);
        echo "<div class='success'>✓ 充值订单表创建成功</div>\n";
    } catch (Exception $e) {
        echo "<div class='error'>✗ 充值订单表创建失败: " . $e->getMessage() . "</div>\n";
    }
    
    // 4. 检查用户表是否需要扩展
    echo "<h3>4. 检查用户表扩展</h3>\n";
    
    try {
        $stmt = $pdo->query("DESCRIBE fa_vppz_user");
        $columns = $stmt->fetchAll();
        $columnNames = array_column($columns, 'Field');
        
        $needColumns = ['balance', 'total_recharge', 'total_reward'];
        $missingColumns = array_diff($needColumns, $columnNames);
        
        if (!empty($missingColumns)) {
            echo "<div class='warning'>- 用户表缺少字段: " . implode(', ', $missingColumns) . "</div>\n";
            
            foreach ($missingColumns as $column) {
                $alterSql = "";
                switch ($column) {
                    case 'balance':
                        $alterSql = "ALTER TABLE `fa_vppz_user` ADD COLUMN `balance` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '账户余额' AFTER `money`";
                        break;
                    case 'total_recharge':
                        $alterSql = "ALTER TABLE `fa_vppz_user` ADD COLUMN `total_recharge` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计充值' AFTER `balance`";
                        break;
                    case 'total_reward':
                        $alterSql = "ALTER TABLE `fa_vppz_user` ADD COLUMN `total_reward` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '累计奖励' AFTER `total_recharge`";
                        break;
                }
                
                if ($alterSql) {
                    try {
                        $pdo->exec($alterSql);
                        echo "<div class='success'>✓ 添加字段 {$column} 成功</div>\n";
                    } catch (Exception $e) {
                        echo "<div class='error'>✗ 添加字段 {$column} 失败: " . $e->getMessage() . "</div>\n";
                    }
                }
            }
        } else {
            echo "<div class='success'>✓ 用户表字段完整</div>\n";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ 检查用户表失败: " . $e->getMessage() . "</div>\n";
    }
    
    // 5. 扩展资金流水表
    echo "<h3>5. 检查资金流水表扩展</h3>\n";
    
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM fa_vppz_money LIKE 'business_type'");
        $column = $stmt->fetch();
        
        if ($column) {
            // 检查是否包含recharge类型
            $enumValues = $column['Type'];
            if (strpos($enumValues, 'recharge') === false) {
                echo "<div class='warning'>- 资金流水表需要添加recharge业务类型</div>\n";
                
                // 获取当前enum值
                preg_match("/enum\((.+)\)/", $enumValues, $matches);
                $currentValues = $matches[1];
                $newValues = $currentValues . ",'recharge'";
                
                $alterSql = "ALTER TABLE `fa_vppz_money` MODIFY COLUMN `business_type` enum({$newValues}) NOT NULL DEFAULT 'order' COMMENT '业务类型'";
                
                try {
                    $pdo->exec($alterSql);
                    echo "<div class='success'>✓ 资金流水表业务类型扩展成功</div>\n";
                } catch (Exception $e) {
                    echo "<div class='error'>✗ 资金流水表业务类型扩展失败: " . $e->getMessage() . "</div>\n";
                }
            } else {
                echo "<div class='success'>✓ 资金流水表已包含recharge类型</div>\n";
            }
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ 检查资金流水表失败: " . $e->getMessage() . "</div>\n";
    }
    
    // 6. 插入测试数据
    echo "<h3>6. 插入测试数据</h3>\n";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM fa_vppz_recharge_package");
        $count = $stmt->fetch()['count'];
        
        if ($count == 0) {
            echo "<div class='info'>- 插入测试套餐数据...</div>\n";
            
            $testPackages = [
                [1, 0, '新手充值套餐', '新用户专享，充100送20', 100.00, 'fixed', 20.00, 20.00, '', 100, 'normal', 1, 0, 0, time(), time(), 0],
                [1, 0, '标准充值套餐', '充值200元，额外赠送10%', 200.00, 'percent', 10.00, 20.00, '', 90, 'normal', 0, 0, 0, time(), time(), 0],
                [1, 0, '超值充值套餐', '充值500元，立享50元奖励', 500.00, 'fixed', 50.00, 50.00, '', 80, 'normal', 1, 0, 0, time(), time(), 0],
                [1, 0, '豪华充值套餐', '充值1000元，享受15%奖励', 1000.00, 'percent', 15.00, 150.00, '', 70, 'normal', 1, 0, 0, time(), time(), 0]
            ];
            
            $sql = "INSERT INTO fa_vppz_recharge_package (app_id, area_id, title, description, amount, reward_type, reward_value, reward_amount, icon, sort, status, is_hot, start_time, end_time, createtime, updatetime, deletetime) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $pdo->prepare($sql);
            
            foreach ($testPackages as $package) {
                $stmt->execute($package);
            }
            
            echo "<div class='success'>✓ 已插入 " . count($testPackages) . " 个测试套餐</div>\n";
        } else {
            echo "<div class='info'>- 已存在 {$count} 个套餐数据</div>\n";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ 插入测试数据失败: " . $e->getMessage() . "</div>\n";
    }
    
    // 7. 验证表创建结果
    echo "<h3>7. 验证表创建结果</h3>\n";
    
    foreach ($tables as $table => $desc) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
            $count = $stmt->fetch()['count'];
            echo "<div class='success'>✓ {$desc} 正常，共 {$count} 条记录</div>\n";
        } catch (Exception $e) {
            echo "<div class='error'>✗ {$desc} 异常: " . $e->getMessage() . "</div>\n";
        }
    }
    
    echo "<div class='success' style='font-size: 18px; margin-top: 20px; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;'>\n";
    echo "🎉 数据库表创建完成！现在可以正常保存充值套餐了！\n";
    echo "</div>\n";
    
    echo "<div class='info' style='margin-top: 20px;'>\n";
    echo "<strong>后台访问链接:</strong><br>\n";
    echo "<a href='http://127.0.0.1:888/admin/vppz/recharge_package' target='_blank'>充值套餐管理</a><br>\n";
    echo "<a href='http://127.0.0.1:888/admin/vppz/recharge_order' target='_blank'>充值订单管理</a><br>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>数据库操作失败: " . $e->getMessage() . "</div>\n";
}

echo "<br><p><em>执行时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
