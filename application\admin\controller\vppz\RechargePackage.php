<?php

namespace app\admin\controller\vppz;

use app\common\controller\Backend;
use think\Db;

/**
 * 充值套餐管理
 *
 * @icon fa fa-gift
 */
class RechargePackage extends Base
{
    /**
     * RechargePackage模型对象
     * @var \app\admin\model\vppz\RechargePackage
     */
    protected $model = null;

    protected $searchFields = ['id', 'title', 'amount'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\vppz\RechargePackage;

        $statusList = ['normal' => '正常', 'hidden' => '隐藏'];
        $rewardTypeList = ['fixed' => '固定金额', 'percent' => '百分比'];
        $isHotList = ['0' => '否', '1' => '是'];

        $this->view->assign("statusList", $statusList);
        $this->view->assign("rewardTypeList", $rewardTypeList);
        $this->view->assign("isHotList", $isHotList);
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            try {
                list($where, $sort, $order, $offset, $limit) = $this->buildparams();

                // 使用Db类直接查询，避免模型问题
                $total = Db::name('vppz_recharge_package')
                    ->where($where)
                    ->count();

                $list = Db::name('vppz_recharge_package')
                    ->where($where)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

                $rows = [];
                foreach ($list as $row) {
                    $item = [
                        'id' => $row['id'],
                        'app_id' => isset($row['app_id']) ? $row['app_id'] : 1,
                        'title' => $row['title'],
                        'description' => isset($row['description']) ? $row['description'] : '',
                        'amount' => $row['amount'],
                        'reward_type' => $row['reward_type'],
                        'reward_value' => $row['reward_value'],
                        'reward_amount' => $row['reward_amount'],
                        'icon' => isset($row['icon']) ? $row['icon'] : '',
                        'sort' => isset($row['sort']) ? $row['sort'] : 0,
                        'status' => $row['status'],
                        'is_hot' => isset($row['is_hot']) ? $row['is_hot'] : 0,
                        'createtime' => $row['createtime'],
                        'updatetime' => isset($row['updatetime']) ? $row['updatetime'] : $row['createtime'],
                        'status_text' => $row['status'] == 'normal' ? '正常' : '隐藏',
                        'reward_type_text' => $row['reward_type'] == 'fixed' ? '固定金额' : '百分比',
                        'is_hot_text' => (isset($row['is_hot']) && $row['is_hot']) ? '是' : '否',
                        'actual_amount_text' => '充' . $row['amount'] . '得' . ($row['amount'] + $row['reward_amount'])
                    ];
                    $rows[] = $item;
                }

                $result = array("total" => $total, "rows" => $rows);
                return json($result);

            } catch (\Exception $e) {
                return json(['total' => 0, 'rows' => [], 'error' => $e->getMessage()]);
            }
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                \think\Db::startTrans();
                try {
                    // 基础验证
                    if (empty($params['title'])) {
                        $this->error('套餐名称不能为空');
                    }
                    if (empty($params['amount']) || $params['amount'] <= 0) {
                        $this->error('充值金额必须大于0');
                    }

                    // 设置默认值
                    $data = [
                        'app_id' => isset($params['app_id']) ? intval($params['app_id']) : 1,
                        'area_id' => isset($params['area_id']) ? intval($params['area_id']) : 0,
                        'title' => trim($params['title']),
                        'description' => isset($params['description']) ? trim($params['description']) : '',
                        'amount' => floatval($params['amount']),
                        'reward_type' => isset($params['reward_type']) ? $params['reward_type'] : 'fixed',
                        'reward_value' => isset($params['reward_value']) ? floatval($params['reward_value']) : 0,
                        'icon' => isset($params['icon']) ? trim($params['icon']) : '',
                        'sort' => isset($params['sort']) ? intval($params['sort']) : 0,
                        'status' => isset($params['status']) ? $params['status'] : 'normal',
                        'is_hot' => isset($params['is_hot']) ? intval($params['is_hot']) : 0,
                        'start_time' => 0,
                        'end_time' => 0,
                        'createtime' => time(),
                        'updatetime' => time(),
                        'deletetime' => 0
                    ];

                    // 自动计算奖励金额
                    if ($data['reward_type'] == 'percent') {
                        $data['reward_amount'] = round($data['amount'] * $data['reward_value'] / 100, 2);
                    } else {
                        $data['reward_amount'] = $data['reward_value'];
                    }

                    // 处理时间字段
                    if (isset($params['start_time']) && $params['start_time']) {
                        $data['start_time'] = strtotime($params['start_time']);
                    }
                    if (isset($params['end_time']) && $params['end_time']) {
                        $data['end_time'] = strtotime($params['end_time']);
                    }

                    // 使用Db类直接插入
                    $result = \think\Db::name('vppz_recharge_package')->insert($data);

                    \think\Db::commit();

                    if ($result) {
                        $this->success();
                    } else {
                        $this->error('保存失败');
                    }
                } catch (\Exception $e) {
                    \think\Db::rollback();
                    $this->error('保存失败: ' . $e->getMessage());
                }
            }
            $this->error('参数不能为空');
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                \think\Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }

                    // 自动计算奖励金额
                    if (isset($params['reward_type']) && isset($params['reward_value']) && isset($params['amount'])) {
                        $params['reward_amount'] = \app\admin\model\vppz\RechargePackage::calculateRewardAmount(
                            $params['amount'], 
                            $params['reward_type'], 
                            $params['reward_value']
                        );
                    }

                    // 处理时间字段
                    if (isset($params['start_time']) && $params['start_time']) {
                        $params['start_time'] = strtotime($params['start_time']);
                    } else {
                        $params['start_time'] = 0;
                    }

                    if (isset($params['end_time']) && $params['end_time']) {
                        $params['end_time'] = strtotime($params['end_time']);
                    } else {
                        $params['end_time'] = 0;
                    }

                    $result = $row->allowField(true)->save($params);
                    \think\Db::commit();
                } catch (\think\exception\PDOException $e) {
                    \think\Db::rollback();
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    \think\Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        // 格式化时间字段用于显示
        if ($row['start_time'] > 0) {
            $row['start_time'] = date('Y-m-d H:i:s', $row['start_time']);
        } else {
            $row['start_time'] = '';
        }

        if ($row['end_time'] > 0) {
            $row['end_time'] = date('Y-m-d H:i:s', $row['end_time']);
        } else {
            $row['end_time'] = '';
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 批量更新
     */
    public function multi($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        if ($ids) {
            $pk = $this->model->getPk();
            $adminIds = $this->getDataLimitAdminIds();
            if (is_array($adminIds)) {
                $this->model->where($this->dataLimitField, 'in', $adminIds);
            }
            $list = $this->model->where($pk, 'in', $ids)->select();

            $count = 0;
            \think\Db::startTrans();
            try {
                foreach ($list as $index => $item) {
                    $count += $item->delete();
                }
                \think\Db::commit();
            } catch (\think\exception\PDOException $e) {
                \think\Db::rollback();
                $this->error($e->getMessage());
            } catch (\think\Exception $e) {
                \think\Db::rollback();
                $this->error($e->getMessage());
            }
            if ($count) {
                $this->success();
            } else {
                $this->error(__('No rows were deleted'));
            }
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }

    /**
     * 计算奖励金额 (AJAX)
     */
    public function calculate_reward()
    {
        $amount = $this->request->post('amount', 0);
        $rewardType = $this->request->post('reward_type', 'fixed');
        $rewardValue = $this->request->post('reward_value', 0);

        $rewardAmount = \app\admin\model\vppz\RechargePackage::calculateRewardAmount(
            $amount, $rewardType, $rewardValue
        );

        $this->success('计算成功', null, ['reward_amount' => $rewardAmount]);
    }
}
