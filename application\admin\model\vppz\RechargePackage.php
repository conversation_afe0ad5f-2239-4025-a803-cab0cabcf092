<?php

namespace app\admin\model\vppz;

use think\Model;
use traits\model\SoftDelete;

class RechargePackage extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'vppz_recharge_package';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text',
        'reward_type_text',
        'is_hot_text',
        'actual_amount_text'
    ];

    /**
     * 状态列表
     */
    public function getStatusList()
    {
        return ['normal' => __('Normal'), 'hidden' => __('Hidden')];
    }

    /**
     * 奖励类型列表
     */
    public function getRewardTypeList()
    {
        return ['fixed' => __('Fixed amount'), 'percent' => __('Percentage')];
    }

    /**
     * 是否热门列表
     */
    public function getIsHotList()
    {
        return ['0' => __('No'), '1' => __('Yes')];
    }

    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 奖励类型文本获取器
     */
    public function getRewardTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['reward_type']) ? $data['reward_type'] : '');
        $list = $this->getRewardTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 是否热门文本获取器
     */
    public function getIsHotTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['is_hot']) ? $data['is_hot'] : '');
        $list = $this->getIsHotList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 实际到账金额文本获取器
     */
    public function getActualAmountTextAttr($value, $data)
    {
        $amount = isset($data['amount']) ? $data['amount'] : 0;
        $rewardAmount = isset($data['reward_amount']) ? $data['reward_amount'] : 0;
        $total = $amount + $rewardAmount;
        return '充' . $amount . '得' . $total;
    }

    /**
     * 计算奖励金额
     * @param float $amount 充值金额
     * @param string $rewardType 奖励类型
     * @param float $rewardValue 奖励值
     * @return float
     */
    public static function calculateRewardAmount($amount, $rewardType, $rewardValue)
    {
        if ($rewardType == 'percent') {
            return round($amount * $rewardValue / 100, 2);
        } else {
            return $rewardValue;
        }
    }

    /**
     * 获取有效的充值套餐
     * @param int $appId 应用ID
     * @param int $areaId 区域ID
     * @return array
     */
    public static function getValidPackages($appId = 0, $areaId = 0)
    {
        $where = [
            'status' => 'normal'
        ];
        
        if ($appId > 0) {
            $where['app_id'] = ['in', [0, $appId]];
        }
        
        if ($areaId > 0) {
            $where['area_id'] = ['in', [0, $areaId]];
        }
        
        $currentTime = time();
        
        return self::where($where)
            ->where(function($query) use ($currentTime) {
                $query->where('start_time', '=', 0)
                      ->whereOr('start_time', '<=', $currentTime);
            })
            ->where(function($query) use ($currentTime) {
                $query->where('end_time', '=', 0)
                      ->whereOr('end_time', '>=', $currentTime);
            })
            ->order('is_hot DESC, sort DESC, id ASC')
            ->select();
    }

    /**
     * 检查套餐是否有效
     * @param int $packageId 套餐ID
     * @param int $appId 应用ID
     * @param int $areaId 区域ID
     * @return bool
     */
    public static function isValidPackage($packageId, $appId = 0, $areaId = 0)
    {
        $package = self::get($packageId);
        if (!$package || $package['status'] != 'normal') {
            return false;
        }
        
        // 检查应用和区域限制
        if ($package['app_id'] > 0 && $package['app_id'] != $appId) {
            return false;
        }
        
        if ($package['area_id'] > 0 && $package['area_id'] != $areaId) {
            return false;
        }
        
        // 检查时间限制
        $currentTime = time();
        if ($package['start_time'] > 0 && $package['start_time'] > $currentTime) {
            return false;
        }
        
        if ($package['end_time'] > 0 && $package['end_time'] < $currentTime) {
            return false;
        }
        
        return true;
    }

    /**
     * 关联应用模型
     */
    public function app()
    {
        return $this->belongsTo('App', 'app_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 关联区域模型
     */
    public function area()
    {
        return $this->belongsTo('Area', 'area_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 自动计算奖励金额
     */
    protected function setRewardValueAttr($value, $data)
    {
        // 当奖励值改变时，自动计算奖励金额
        if (isset($data['amount']) && isset($data['reward_type'])) {
            $this->data['reward_amount'] = self::calculateRewardAmount(
                $data['amount'], 
                $data['reward_type'], 
                $value
            );
        }
        return $value;
    }
}
