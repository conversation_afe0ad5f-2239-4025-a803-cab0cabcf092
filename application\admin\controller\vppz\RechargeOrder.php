<?php

namespace app\admin\controller\vppz;

use app\common\controller\Backend;

/**
 * 充值订单管理
 *
 * @icon fa fa-list-alt
 */
class RechargeOrder extends Base
{
    /**
     * RechargeOrder模型对象
     * @var \app\admin\model\vppz\RechargeOrder
     */
    protected $model = null;

    protected $searchFields = ['id', 'order_no', 'trade_no', 'user.nickname', 'user.mobile'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\vppz\RechargeOrder;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("payTypeList", $this->model->getPayTypeList());
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = $this->model
                ->where($where)
                ->count();

            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();

            $rows = [];
            foreach ($list as $row) {
                $payTypeMap = ['wechat' => '微信支付', 'alipay' => '支付宝', 'balance' => '余额支付'];
                $statusMap = ['pending' => '待支付', 'paid' => '已支付', 'failed' => '支付失败', 'refunded' => '已退款'];

                $item = [
                    'id' => $row['id'],
                    'app_id' => $row['app_id'],
                    'user_id' => $row['user_id'],
                    'package_id' => $row['package_id'],
                    'order_no' => $row['order_no'],
                    'transaction_id' => $row['transaction_id'],
                    'amount' => $row['amount'],
                    'reward_amount' => $row['reward_amount'],
                    'total_amount' => $row['total_amount'],
                    'pay_type' => $row['pay_type'],
                    'status' => $row['status'],
                    'pay_time' => $row['pay_time'],
                    'createtime' => $row['createtime'],
                    'status_text' => isset($statusMap[$row['status']]) ? $statusMap[$row['status']] : $row['status'],
                    'pay_type_text' => isset($payTypeMap[$row['pay_type']]) ? $payTypeMap[$row['pay_type']] : $row['pay_type']
                ];
                $rows[] = $item;
            }

            $result = array("total" => $total, "rows" => $rows);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 退款处理
     */
    public function refund($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        if ($row['status'] != 'paid') {
            $this->error('只有已支付的订单才能退款');
        }

        if ($this->request->isPost()) {
            $reason = $this->request->post('reason', '');
            
            \think\Db::startTrans();
            try {
                // 更新订单状态
                $row->save([
                    'status' => 'refunded',
                    'remark' => '退款原因：' . $reason,
                    'updatetime' => time()
                ]);

                // 扣减用户余额
                $user = \app\admin\model\vppz\User::get($row['user_id']);
                if ($user && $user['balance'] >= $row['total_amount']) {
                    $user->save([
                        'balance' => $user['balance'] - $row['total_amount'],
                        'total_recharge' => $user['total_recharge'] - $row['amount'],
                        'total_reward' => $user['total_reward'] - $row['reward_amount']
                    ]);

                    // 记录资金流水
                    \app\admin\model\vppz\Money::create([
                        'app_id' => $row['app_id'],
                        'area_id' => $row['area_id'],
                        'user_id' => $row['user_id'],
                        'biz' => 'recharge',
                        'biz_id' => $row['id'],
                        'money' => -$row['total_amount'],
                        'before' => $user['balance'],
                        'after' => $user['balance'] - $row['total_amount'],
                        'memo' => '充值退款：' . $row['amount'] . '元，扣减奖励：' . $row['reward_amount'] . '元',
                        'createtime' => time(),
                        'updatetime' => time()
                    ]);
                } else {
                    throw new \Exception('用户余额不足，无法退款');
                }

                \think\Db::commit();
                $this->success('退款成功');
            } catch (\Exception $e) {
                \think\Db::rollback();
                $this->error('退款失败：' . $e->getMessage());
            }
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 统计数据
     */
    public function statistics()
    {
        $startTime = $this->request->get('start_time', '');
        $endTime = $this->request->get('end_time', '');
        $status = $this->request->get('status', '');

        $where = [];
        
        if ($startTime) {
            $where['createtime'] = ['>=', strtotime($startTime)];
        }
        
        if ($endTime) {
            $where['createtime'] = ['<=', strtotime($endTime . ' 23:59:59')];
        }
        
        if ($status) {
            $where['status'] = $status;
        }

        $statistics = \app\admin\model\vppz\RechargeOrder::getStatistics($where);
        
        // 按日期统计
        $dailyStats = $this->model
            ->where($where)
            ->field('FROM_UNIXTIME(createtime, "%Y-%m-%d") as date, COUNT(*) as count, SUM(amount) as amount')
            ->group('FROM_UNIXTIME(createtime, "%Y-%m-%d")')
            ->order('date DESC')
            ->limit(30)
            ->select();

        // 按支付方式统计
        $payTypeStats = $this->model
            ->where($where)
            ->where('status', 'paid')
            ->field('pay_type, COUNT(*) as count, SUM(amount) as amount')
            ->group('pay_type')
            ->select();

        $this->view->assign('statistics', $statistics);
        $this->view->assign('dailyStats', $dailyStats);
        $this->view->assign('payTypeStats', $payTypeStats);
        
        return $this->view->fetch();
    }

    /**
     * 导出订单
     */
    public function export()
    {
        $startTime = $this->request->get('start_time', '');
        $endTime = $this->request->get('end_time', '');
        $status = $this->request->get('status', '');

        $where = [];
        
        if ($startTime) {
            $where['createtime'] = ['>=', strtotime($startTime)];
        }
        
        if ($endTime) {
            $where['createtime'] = ['<=', strtotime($endTime . ' 23:59:59')];
        }
        
        if ($status) {
            $where['status'] = $status;
        }

        $list = $this->model
            ->with(['user', 'package'])
            ->where($where)
            ->order('createtime DESC')
            ->select();

        $header = [
            '订单号', '用户昵称', '用户手机', '套餐名称', '充值金额', '奖励金额', '到账金额', 
            '支付方式', '订单状态', '支付时间', '创建时间', '备注'
        ];

        $data = [];
        foreach ($list as $row) {
            $data[] = [
                $row['order_no'],
                $row['user']['nickname'] ?? '',
                $row['user']['mobile'] ?? '',
                $row['package']['title'] ?? '',
                $row['amount'],
                $row['reward_amount'],
                $row['total_amount'],
                $row['pay_type_text'],
                $row['status_text'],
                $row['pay_time_text'],
                date('Y-m-d H:i:s', $row['createtime']),
                $row['remark']
            ];
        }

        // 这里可以使用PHPExcel或其他库来生成Excel文件
        // 简单起见，这里返回CSV格式
        $filename = '充值订单_' . date('YmdHis') . '.csv';
        
        header('Content-Type: application/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        fputcsv($output, $header);
        
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit;
    }

    /**
     * 批量操作（禁用删除）
     */
    public function multi($ids = "")
    {
        $this->error('充值订单不允许删除操作');
    }

    /**
     * 删除（禁用）
     */
    public function del($ids = "")
    {
        $this->error('充值订单不允许删除操作');
    }
}
