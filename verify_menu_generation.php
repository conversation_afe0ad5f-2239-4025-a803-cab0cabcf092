<?php
// 验证菜单生成结果
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>验证充值功能菜单生成结果</h2>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .link{margin:10px 0;}</style>\n";

try {
    // 数据库连接配置
    $dsn = "mysql:host=127.0.0.1;port=3306;dbname=fast;charset=utf8mb4";
    $username = "root";
    $password = "tHucJ7ggCMFFC72P";
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✓ 数据库连接成功</div>\n";
    
    // 检查充值相关菜单
    echo "<h3>1. 检查充值相关菜单</h3>\n";
    
    $stmt = $pdo->prepare("SELECT id, pid, name, title, icon, ismenu, status FROM fa_auth_rule WHERE name LIKE 'vppz/recharge%' ORDER BY pid, weigh DESC");
    $stmt->execute();
    $menus = $stmt->fetchAll();
    
    if (count($menus) > 0) {
        echo "<div class='success'>✓ 找到 " . count($menus) . " 个充值相关菜单</div>\n";
        echo "<table border='1' style='border-collapse:collapse; margin:10px 0; width:100%;'>\n";
        echo "<tr><th>ID</th><th>父级ID</th><th>权限名称</th><th>菜单标题</th><th>图标</th><th>是否菜单</th><th>状态</th></tr>\n";
        foreach ($menus as $menu) {
            $isMenuText = $menu['ismenu'] ? '是' : '否';
            $statusText = $menu['status'] == 'normal' ? '正常' : $menu['status'];
            echo "<tr>";
            echo "<td>{$menu['id']}</td>";
            echo "<td>{$menu['pid']}</td>";
            echo "<td>{$menu['name']}</td>";
            echo "<td>{$menu['title']}</td>";
            echo "<td>{$menu['icon']}</td>";
            echo "<td>{$isMenuText}</td>";
            echo "<td>{$statusText}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<div class='error'>✗ 没有找到充值相关菜单</div>\n";
    }
    
    // 检查菜单层级结构
    echo "<h3>2. 检查菜单层级结构</h3>\n";
    
    // 查找主菜单
    $mainMenus = array_filter($menus, function($menu) {
        return $menu['ismenu'] == 1 && in_array($menu['name'], ['vppz/recharge_package', 'vppz/recharge_order']);
    });
    
    foreach ($mainMenus as $mainMenu) {
        echo "<div class='info'><strong>{$mainMenu['title']} (ID: {$mainMenu['id']})</strong></div>\n";
        
        // 查找子菜单
        $subMenus = array_filter($menus, function($menu) use ($mainMenu) {
            return $menu['pid'] == $mainMenu['id'];
        });
        
        if (count($subMenus) > 0) {
            echo "<div class='success'>  ✓ 找到 " . count($subMenus) . " 个子菜单</div>\n";
            foreach ($subMenus as $subMenu) {
                echo "<div class='info'>    - {$subMenu['title']} ({$subMenu['name']})</div>\n";
            }
        } else {
            echo "<div class='error'>  ✗ 没有找到子菜单</div>\n";
        }
    }
    
    // 检查超级管理员权限
    echo "<h3>3. 检查超级管理员权限</h3>\n";
    
    $stmt = $pdo->prepare("SELECT rules FROM fa_auth_group WHERE id = 1");
    $stmt->execute();
    $adminGroup = $stmt->fetch();
    
    if ($adminGroup) {
        $rules = explode(',', $adminGroup['rules']);
        $rechargeMenuIds = array_column($menus, 'id');
        $missingPermissions = [];
        
        foreach ($rechargeMenuIds as $menuId) {
            if (!in_array($menuId, $rules)) {
                $missingPermissions[] = $menuId;
            }
        }
        
        if (empty($missingPermissions)) {
            echo "<div class='success'>✓ 超级管理员拥有所有充值功能权限</div>\n";
        } else {
            echo "<div class='error'>✗ 超级管理员缺少权限，缺失的菜单ID: " . implode(', ', $missingPermissions) . "</div>\n";
            
            // 自动添加权限
            $newRules = array_unique(array_merge($rules, $rechargeMenuIds));
            $newRulesStr = implode(',', $newRules);
            
            $stmt = $pdo->prepare("UPDATE fa_auth_group SET rules = ? WHERE id = 1");
            $stmt->execute([$newRulesStr]);
            
            echo "<div class='success'>✓ 已自动为超级管理员添加缺失权限</div>\n";
        }
    }
    
    // 生成后台访问链接
    echo "<h3>4. 后台访问链接</h3>\n";
    
    $baseUrl = "http://127.0.0.1:888/admin";
    
    echo "<div class='link'><strong>后台登录:</strong> <a href='{$baseUrl}' target='_blank'>{$baseUrl}</a></div>\n";
    
    foreach ($mainMenus as $mainMenu) {
        $url = $baseUrl . '/' . $mainMenu['name'];
        echo "<div class='link'><strong>{$mainMenu['title']}:</strong> <a href='{$url}' target='_blank'>{$url}</a></div>\n";
    }
    
    // 检查vppz主菜单
    echo "<h3>5. 检查vppz主菜单</h3>\n";
    
    $stmt = $pdo->prepare("SELECT id, name, title FROM fa_auth_rule WHERE name = 'vppz' AND ismenu = 1");
    $stmt->execute();
    $vppzMainMenu = $stmt->fetch();
    
    if ($vppzMainMenu) {
        echo "<div class='success'>✓ vppz主菜单存在 (ID: {$vppzMainMenu['id']})</div>\n";
        
        // 检查充值菜单是否在vppz下
        $rechargeMainMenus = array_filter($mainMenus, function($menu) use ($vppzMainMenu) {
            return $menu['pid'] == $vppzMainMenu['id'];
        });
        
        if (count($rechargeMainMenus) > 0) {
            echo "<div class='success'>✓ 充值菜单已正确添加到vppz主菜单下</div>\n";
        } else {
            echo "<div class='error'>✗ 充值菜单未添加到vppz主菜单下</div>\n";
            
            // 自动修正菜单层级
            foreach ($mainMenus as $mainMenu) {
                $stmt = $pdo->prepare("UPDATE fa_auth_rule SET pid = ? WHERE id = ?");
                $stmt->execute([$vppzMainMenu['id'], $mainMenu['id']]);
            }
            echo "<div class='success'>✓ 已自动修正菜单层级</div>\n";
        }
    } else {
        echo "<div class='error'>✗ vppz主菜单不存在</div>\n";
    }
    
    echo "<h3>菜单生成验证完成！</h3>\n";
    echo "<div class='info'>请刷新后台页面，在左侧菜单中查看 '嘀嗒陪护' 下的充值管理功能。</div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>验证失败: " . $e->getMessage() . "</div>\n";
}

echo "<br><p><em>验证时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
