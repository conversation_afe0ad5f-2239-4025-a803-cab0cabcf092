<?php
// 直接调试控制器
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>直接调试充值套餐控制器</h2>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border:1px solid #ddd;}</style>\n";

try {
    // 数据库连接配置
    $dsn = "mysql:host=127.0.0.1;port=3306;dbname=fast;charset=utf8mb4";
    $username = "root";
    $password = "tHucJ7ggCMFFC72P";
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✓ 数据库连接成功</div>\n";
    
    // 1. 直接查询数据库
    echo "<h3>1. 直接查询数据库</h3>\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM fa_vppz_recharge_package");
    $count = $stmt->fetch()['count'];
    echo "<div class='info'>套餐总数: {$count}</div>\n";
    
    if ($count == 0) {
        // 插入测试数据
        echo "<div class='info'>插入测试数据...</div>\n";
        $sql = "INSERT INTO fa_vppz_recharge_package (app_id, title, description, amount, reward_type, reward_value, reward_amount, icon, sort, status, is_hot, createtime, updatetime) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            1, '测试套餐', '测试描述', 100.00, 'fixed', 20.00, 20.00, '', 100, 'normal', 1, time(), time()
        ]);
        echo "<div class='success'>✓ 已插入测试数据</div>\n";
    }
    
    // 2. 模拟控制器查询
    echo "<h3>2. 模拟控制器查询</h3>\n";
    
    $where = "1=1";
    $sort = "sort";
    $order = "desc";
    $offset = 0;
    $limit = 10;
    
    // 查询总数
    $totalSql = "SELECT COUNT(*) as total FROM fa_vppz_recharge_package WHERE {$where}";
    $stmt = $pdo->query($totalSql);
    $total = $stmt->fetch()['total'];
    echo "<div class='info'>查询总数: {$total}</div>\n";
    
    // 查询列表
    $listSql = "SELECT * FROM fa_vppz_recharge_package WHERE {$where} ORDER BY {$sort} {$order} LIMIT {$offset}, {$limit}";
    $stmt = $pdo->query($listSql);
    $list = $stmt->fetchAll();
    echo "<div class='info'>查询结果数: " . count($list) . "</div>\n";
    
    // 3. 构建返回数据
    echo "<h3>3. 构建返回数据</h3>\n";
    
    $rows = [];
    foreach ($list as $row) {
        $item = [
            'id' => $row['id'],
            'app_id' => $row['app_id'],
            'title' => $row['title'],
            'description' => $row['description'],
            'amount' => $row['amount'],
            'reward_type' => $row['reward_type'],
            'reward_value' => $row['reward_value'],
            'reward_amount' => $row['reward_amount'],
            'icon' => $row['icon'],
            'sort' => $row['sort'],
            'status' => $row['status'],
            'is_hot' => $row['is_hot'],
            'createtime' => $row['createtime'],
            'updatetime' => $row['updatetime'],
            'status_text' => $row['status'] == 'normal' ? '正常' : '隐藏',
            'reward_type_text' => $row['reward_type'] == 'fixed' ? '固定金额' : '百分比',
            'is_hot_text' => $row['is_hot'] ? '是' : '否',
            'actual_amount_text' => '充' . $row['amount'] . '得' . ($row['amount'] + $row['reward_amount'])
        ];
        $rows[] = $item;
    }
    
    $result = array("total" => $total, "rows" => $rows);
    
    echo "<div class='success'>✓ 数据构建成功</div>\n";
    echo "<h3>4. JSON输出测试</h3>\n";
    
    $json = json_encode($result, JSON_UNESCAPED_UNICODE);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<div class='success'>✓ JSON编码成功</div>\n";
        echo "<pre>" . $json . "</pre>\n";
    } else {
        echo "<div class='error'>✗ JSON编码失败: " . json_last_error_msg() . "</div>\n";
    }
    
    // 5. 创建简化的控制器测试
    echo "<h3>5. 创建简化控制器</h3>\n";
    
    $simpleController = '<?php
namespace app\admin\controller\vppz;

use app\common\controller\Backend;
use think\Db;

class RechargePackageSimple extends Backend
{
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = Db::name("vppz_recharge_package");
    }

    public function index()
    {
        if ($this->request->isAjax()) {
            $page = $this->request->get("offset", 0);
            $limit = $this->request->get("limit", 10);
            
            $total = $this->model->count();
            $list = $this->model->order("sort desc")->limit($page, $limit)->select();
            
            $rows = [];
            foreach ($list as $row) {
                $rows[] = [
                    "id" => $row["id"],
                    "title" => $row["title"],
                    "amount" => $row["amount"],
                    "reward_amount" => $row["reward_amount"],
                    "status" => $row["status"],
                    "status_text" => $row["status"] == "normal" ? "正常" : "隐藏",
                    "createtime" => $row["createtime"]
                ];
            }
            
            return json(["total" => $total, "rows" => $rows]);
        }
        return $this->view->fetch();
    }
}';
    
    file_put_contents('application/admin/controller/vppz/RechargePackageSimple.php', $simpleController);
    echo "<div class='success'>✓ 已创建简化控制器</div>\n";
    
    // 6. 测试建议
    echo "<h3>6. 测试建议</h3>\n";
    echo "<div class='info'>\n";
    echo "<strong>下一步测试:</strong><br>\n";
    echo "1. 访问简化控制器: <a href='http://127.0.0.1:888/admin/vppz/recharge_package_simple' target='_blank'>简化版本</a><br>\n";
    echo "2. 如果简化版本正常，说明原控制器有问题<br>\n";
    echo "3. 如果简化版本也有问题，说明是环境或权限问题<br>\n";
    echo "4. 检查FastAdmin的错误日志文件<br>\n";
    echo "5. 确保登录状态和权限正确<br>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>调试失败: " . $e->getMessage() . "</div>\n";
}

echo "<br><p><em>调试时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
