<?php
// 测试充值功能
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>测试充值功能</h2>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";

try {
    // 数据库连接配置
    $dsn = "mysql:host=127.0.0.1;port=3306;dbname=fast;charset=utf8mb4";
    $username = "root";
    $password = "tHucJ7ggCMFFC72P";
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✓ 数据库连接成功</div>\n";
    
    // 测试1: 检查数据表是否创建成功
    echo "<h3>1. 检查数据表结构</h3>\n";
    
    $tables = [
        'fa_vppz_recharge_package' => '充值套餐表',
        'fa_vppz_recharge_order' => '充值订单表'
    ];
    
    foreach ($tables as $table => $desc) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='success'>✓ {$desc} 存在</div>\n";
                
                // 检查记录数
                $countStmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
                $count = $countStmt->fetch();
                echo "<div class='info'>  - 记录数: {$count['count']}</div>\n";
            } else {
                echo "<div class='error'>✗ {$desc} 不存在</div>\n";
            }
        } catch (Exception $e) {
            echo "<div class='error'>✗ 检查 {$desc} 时出错: " . $e->getMessage() . "</div>\n";
        }
    }
    
    // 测试2: 检查用户表字段
    echo "<h3>2. 检查用户表扩展字段</h3>\n";
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM fa_vppz_user LIKE 'balance'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✓ 用户表余额字段存在</div>\n";
        } else {
            echo "<div class='error'>✗ 用户表余额字段不存在</div>\n";
        }
        
        $stmt = $pdo->query("SHOW COLUMNS FROM fa_vppz_user LIKE 'total_recharge'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='success'>✓ 用户表累计充值字段存在</div>\n";
        } else {
            echo "<div class='error'>✗ 用户表累计充值字段不存在</div>\n";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ 检查用户表字段时出错: " . $e->getMessage() . "</div>\n";
    }
    
    // 测试3: 检查充值套餐数据
    echo "<h3>3. 检查充值套餐数据</h3>\n";
    try {
        $stmt = $pdo->query("SELECT * FROM fa_vppz_recharge_package ORDER BY sort DESC");
        $packages = $stmt->fetchAll();
        
        if (count($packages) > 0) {
            echo "<div class='success'>✓ 找到 " . count($packages) . " 个充值套餐</div>\n";
            echo "<table border='1' style='border-collapse:collapse; margin:10px 0;'>\n";
            echo "<tr><th>ID</th><th>套餐名称</th><th>充值金额</th><th>奖励金额</th><th>状态</th><th>是否热门</th></tr>\n";
            foreach ($packages as $package) {
                echo "<tr>";
                echo "<td>{$package['id']}</td>";
                echo "<td>{$package['title']}</td>";
                echo "<td>￥{$package['amount']}</td>";
                echo "<td>￥{$package['reward_amount']}</td>";
                echo "<td>{$package['status']}</td>";
                echo "<td>" . ($package['is_hot'] ? '是' : '否') . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        } else {
            echo "<div class='error'>✗ 没有找到充值套餐数据</div>\n";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ 检查充值套餐数据时出错: " . $e->getMessage() . "</div>\n";
    }
    
    // 测试4: 检查文件是否存在
    echo "<h3>4. 检查功能文件</h3>\n";
    
    $files = [
        'application/admin/model/vppz/RechargePackage.php' => '充值套餐模型',
        'application/admin/model/vppz/RechargeOrder.php' => '充值订单模型',
        'application/admin/controller/vppz/RechargePackage.php' => '充值套餐控制器',
        'application/admin/controller/vppz/RechargeOrder.php' => '充值订单控制器',
        'application/api/controller/vppz/Recharge.php' => '充值API控制器',
        'application/admin/view/vppz/recharge_package/index.html' => '充值套餐视图',
        'application/admin/view/vppz/recharge_order/index.html' => '充值订单视图',
        'public/assets/js/backend/vppz/recharge_package.js' => '充值套餐JS',
        'public/assets/js/backend/vppz/recharge_order.js' => '充值订单JS'
    ];
    
    foreach ($files as $file => $desc) {
        if (file_exists($file)) {
            echo "<div class='success'>✓ {$desc} 文件存在</div>\n";
        } else {
            echo "<div class='error'>✗ {$desc} 文件不存在</div>\n";
        }
    }
    
    // 测试5: 检查菜单配置
    echo "<h3>5. 检查菜单配置</h3>\n";
    try {
        $vppzFile = 'addons/vppz/Vppz.php';
        if (file_exists($vppzFile)) {
            $content = file_get_contents($vppzFile);
            if (strpos($content, 'recharge_package') !== false) {
                echo "<div class='success'>✓ 充值套餐菜单配置已添加</div>\n";
            } else {
                echo "<div class='error'>✗ 充值套餐菜单配置未添加</div>\n";
            }
            
            if (strpos($content, 'recharge_order') !== false) {
                echo "<div class='success'>✓ 充值订单菜单配置已添加</div>\n";
            } else {
                echo "<div class='error'>✗ 充值订单菜单配置未添加</div>\n";
            }
        } else {
            echo "<div class='error'>✗ Vppz.php 文件不存在</div>\n";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ 检查菜单配置时出错: " . $e->getMessage() . "</div>\n";
    }
    
    // 测试6: 检查支付回调扩展
    echo "<h3>6. 检查支付回调扩展</h3>\n";
    try {
        $payFile = 'addons/vppz/controller/Pay.php';
        if (file_exists($payFile)) {
            $content = file_get_contents($payFile);
            if (strpos($content, 'payResultRCH') !== false) {
                echo "<div class='success'>✓ 充值支付回调方法已添加</div>\n";
            } else {
                echo "<div class='error'>✗ 充值支付回调方法未添加</div>\n";
            }
            
            if (strpos($content, 'RechargeOrderModel') !== false) {
                echo "<div class='success'>✓ 充值订单模型引用已添加</div>\n";
            } else {
                echo "<div class='error'>✗ 充值订单模型引用未添加</div>\n";
            }
        } else {
            echo "<div class='error'>✗ Pay.php 文件不存在</div>\n";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ 检查支付回调扩展时出错: " . $e->getMessage() . "</div>\n";
    }
    
    echo "<h3>功能测试完成！</h3>\n";
    echo "<div class='info'>如果所有项目都显示 ✓，说明充值功能开发完成并可以正常使用。</div>\n";
    echo "<div class='info'>接下来可以访问后台管理界面测试充值套餐和订单管理功能。</div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>测试失败: " . $e->getMessage() . "</div>\n";
}

echo "<br><p><em>测试时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
