<?php
// 调试充值套餐控制器错误
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>调试充值套餐控制器错误</h2>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .warning{color:orange;}</style>\n";

try {
    // 数据库连接配置
    $dsn = "mysql:host=127.0.0.1;port=3306;dbname=fast;charset=utf8mb4";
    $username = "root";
    $password = "tHucJ7ggCMFFC72P";
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✓ 数据库连接成功</div>\n";
    
    // 1. 检查充值套餐表结构
    echo "<h3>1. 检查充值套餐表结构</h3>\n";
    
    $stmt = $pdo->query("DESCRIBE fa_vppz_recharge_package");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse:collapse; margin:10px 0; width:100%;'>\n";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>\n";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // 检查是否缺少必要字段
    $requiredFields = ['app_id', 'area_id'];
    $existingFields = array_column($columns, 'Field');
    $missingFields = array_diff($requiredFields, $existingFields);
    
    if (empty($missingFields)) {
        echo "<div class='success'>✓ 所有必要字段都存在</div>\n";
    } else {
        echo "<div class='error'>✗ 缺少字段: " . implode(', ', $missingFields) . "</div>\n";
        
        // 自动添加缺少的字段
        foreach ($missingFields as $field) {
            try {
                if ($field == 'app_id') {
                    $pdo->exec("ALTER TABLE fa_vppz_recharge_package ADD COLUMN app_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '应用ID' AFTER id");
                    echo "<div class='success'>✓ 已添加 app_id 字段</div>\n";
                } elseif ($field == 'area_id') {
                    $pdo->exec("ALTER TABLE fa_vppz_recharge_package ADD COLUMN area_id int(10) unsigned NOT NULL DEFAULT 0 COMMENT '区域ID' AFTER app_id");
                    echo "<div class='success'>✓ 已添加 area_id 字段</div>\n";
                }
            } catch (Exception $e) {
                echo "<div class='error'>✗ 添加字段 {$field} 失败: " . $e->getMessage() . "</div>\n";
            }
        }
    }
    
    // 2. 测试模型加载
    echo "<h3>2. 测试模型加载</h3>\n";
    
    // 模拟ThinkPHP环境
    define('APP_PATH', __DIR__ . '/application/');
    define('ROOT_PATH', __DIR__ . '/');
    define('RUNTIME_PATH', __DIR__ . '/runtime/');
    define('CONF_PATH', __DIR__ . '/application/');
    
    // 检查模型文件
    $modelFile = 'application/admin/model/vppz/RechargePackage.php';
    if (file_exists($modelFile)) {
        echo "<div class='success'>✓ RechargePackage模型文件存在</div>\n";
        
        // 检查模型语法
        $modelContent = file_get_contents($modelFile);
        if (strpos($modelContent, 'class RechargePackage') !== false) {
            echo "<div class='success'>✓ 模型类定义正确</div>\n";
        } else {
            echo "<div class='error'>✗ 模型类定义有问题</div>\n";
        }
        
        // 检查关联关系
        if (strpos($modelContent, 'function app()') !== false && strpos($modelContent, 'function area()') !== false) {
            echo "<div class='success'>✓ 关联关系定义存在</div>\n";
        } else {
            echo "<div class='error'>✗ 关联关系定义缺失</div>\n";
        }
    } else {
        echo "<div class='error'>✗ RechargePackage模型文件不存在</div>\n";
    }
    
    // 3. 检查关联表
    echo "<h3>3. 检查关联表</h3>\n";
    
    $relatedTables = ['fa_vppz_app', 'fa_vppz_area'];
    foreach ($relatedTables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='success'>✓ 关联表 {$table} 存在</div>\n";
                
                // 检查记录数
                $countStmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
                $count = $countStmt->fetch();
                echo "<div class='info'>  - 记录数: {$count['count']}</div>\n";
            } else {
                echo "<div class='error'>✗ 关联表 {$table} 不存在</div>\n";
            }
        } catch (Exception $e) {
            echo "<div class='error'>✗ 检查表 {$table} 时出错: " . $e->getMessage() . "</div>\n";
        }
    }
    
    // 4. 测试基础查询
    echo "<h3>4. 测试基础查询</h3>\n";
    
    try {
        $stmt = $pdo->query("SELECT * FROM fa_vppz_recharge_package LIMIT 1");
        $result = $stmt->fetch();
        if ($result) {
            echo "<div class='success'>✓ 基础查询成功</div>\n";
            echo "<div class='info'>示例记录: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "</div>\n";
        } else {
            echo "<div class='warning'>⚠️ 表中暂无数据</div>\n";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ 基础查询失败: " . $e->getMessage() . "</div>\n";
    }
    
    // 5. 测试关联查询
    echo "<h3>5. 测试关联查询</h3>\n";
    
    try {
        $sql = "SELECT p.*, a.name as app_name, ar.name as area_name 
                FROM fa_vppz_recharge_package p 
                LEFT JOIN fa_vppz_app a ON p.app_id = a.id 
                LEFT JOIN fa_vppz_area ar ON p.area_id = ar.id 
                LIMIT 1";
        $stmt = $pdo->query($sql);
        $result = $stmt->fetch();
        echo "<div class='success'>✓ 关联查询语法正确</div>\n";
        if ($result) {
            echo "<div class='info'>关联查询结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "</div>\n";
        }
    } catch (Exception $e) {
        echo "<div class='error'>✗ 关联查询失败: " . $e->getMessage() . "</div>\n";
    }
    
    // 6. 检查控制器文件
    echo "<h3>6. 检查控制器文件</h3>\n";
    
    $controllerFile = 'application/admin/controller/vppz/RechargePackage.php';
    if (file_exists($controllerFile)) {
        echo "<div class='success'>✓ 控制器文件存在</div>\n";
        
        $controllerContent = file_get_contents($controllerFile);
        
        // 检查命名空间
        if (strpos($controllerContent, 'namespace app\admin\controller\vppz;') !== false) {
            echo "<div class='success'>✓ 命名空间正确</div>\n";
        } else {
            echo "<div class='error'>✗ 命名空间有问题</div>\n";
        }
        
        // 检查继承关系
        if (strpos($controllerContent, 'extends Base') !== false) {
            echo "<div class='success'>✓ 继承关系正确</div>\n";
        } else {
            echo "<div class='error'>✗ 继承关系有问题</div>\n";
        }
        
        // 检查模型初始化
        if (strpos($controllerContent, 'new \app\admin\model\vppz\RechargePackage') !== false) {
            echo "<div class='success'>✓ 模型初始化正确</div>\n";
        } else {
            echo "<div class='error'>✗ 模型初始化有问题</div>\n";
        }
    } else {
        echo "<div class='error'>✗ 控制器文件不存在</div>\n";
    }
    
    // 7. 生成修复建议
    echo "<h3>7. 修复建议</h3>\n";
    
    echo "<div class='info'>\n";
    echo "<strong>可能的解决方案:</strong><br>\n";
    echo "1. 确保数据库表结构完整，包含app_id和area_id字段<br>\n";
    echo "2. 检查模型关联关系是否正确定义<br>\n";
    echo "3. 确保关联的App和Area模型存在且可用<br>\n";
    echo "4. 检查控制器中的关联查询语法<br>\n";
    echo "5. 查看FastAdmin的错误日志获取详细错误信息<br>\n";
    echo "</div>\n";
    
    // 8. 提供直接访问链接
    echo "<h3>8. 测试链接</h3>\n";
    
    $testUrl = "http://127.0.0.1:888/admin/vppz/recharge_package";
    echo "<div class='info'><strong>后台访问链接:</strong> <a href='{$testUrl}' target='_blank'>{$testUrl}</a></div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>调试失败: " . $e->getMessage() . "</div>\n";
}

echo "<br><p><em>调试时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
