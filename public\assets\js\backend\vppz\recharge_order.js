define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数
            Table.api.init({
                extend: {
                    index_url: 'vppz/recharge_order/index' + location.search,
                    detail_url: 'vppz/recharge_order/detail',
                    refund_url: 'vppz/recharge_order/refund',
                    statistics_url: 'vppz/recharge_order/statistics',
                    export_url: 'vppz/recharge_order/export',
                    table: 'vppz_recharge_order',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'createtime',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: false},
                        {field: 'order_no', title: __('Order no'), operate: 'LIKE'},
                        {field: 'user.nickname', title: __('User'), operate: 'LIKE'},
                        {field: 'user.mobile', title: __('Mobile'), operate: 'LIKE'},
                        {field: 'package.title', title: __('Package'), operate: 'LIKE'},
                        {field: 'amount', title: __('Amount'), operate: 'BETWEEN', sortable: true},
                        {field: 'reward_amount', title: __('Reward amount'), operate: false},
                        {field: 'total_amount', title: __('Total amount'), operate: false},
                        {field: 'pay_type', title: __('Pay type'), searchList: {"wechat":__("WeChat"),"alipay":__("Alipay"),"balance":__("Balance")}, formatter: function(value, row, index) {
                            return $('#paytypetpl').html().replace(/{{d\.pay_type}}/g, row.pay_type).replace(/{{d\.pay_type_text}}/g, row.pay_type_text);
                        }},
                        {field: 'status', title: __('Status'), searchList: {"pending":__("Pending"),"paid":__("Paid"),"failed":__("Failed"),"refunded":__("Refunded")}, formatter: function(value, row, index) {
                            return $('#statustpl').html().replace(/{{d\.status}}/g, row.status).replace(/{{d\.status_text}}/g, row.status_text);
                        }},
                        {field: 'pay_time_text', title: __('Pay time'), operate: false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, 
                            buttons: [
                                {
                                    name: 'detail',
                                    text: __('Detail'),
                                    title: __('Detail'),
                                    classname: 'btn btn-xs btn-primary btn-dialog',
                                    icon: 'fa fa-list',
                                    url: 'vppz/recharge_order/detail'
                                },
                                {
                                    name: 'refund',
                                    text: __('Refund'),
                                    title: __('Refund'),
                                    classname: 'btn btn-xs btn-danger btn-dialog',
                                    icon: 'fa fa-undo',
                                    url: 'vppz/recharge_order/refund',
                                    visible: function (row) {
                                        return row.status == 'paid';
                                    }
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 绑定导出按钮事件
            $(document).on('click', '.btn-export', function() {
                var startTime = $('input[name="createtime"]').val().split(' - ')[0] || '';
                var endTime = $('input[name="createtime"]').val().split(' - ')[1] || '';
                var status = $('select[name="status"]').val() || '';
                
                var url = $.fn.bootstrapTable.defaults.extend.export_url + '?start_time=' + startTime + '&end_time=' + endTime + '&status=' + status;
                window.open(url);
            });
            
            // 绑定详情按钮事件
            $(document).on('click', '.btn-detail', function() {
                var ids = Table.api.selectedids(table);
                if (ids.length == 1) {
                    Fast.api.open($.fn.bootstrapTable.defaults.extend.detail_url + '?ids=' + ids.join(','), __('Detail'), {
                        area: ['80%', '80%']
                    });
                } else {
                    Toastr.error(__('Please choose one item'));
                }
            });
            
            // 绑定退款按钮事件
            $(document).on('click', '.btn-refund', function() {
                var ids = Table.api.selectedids(table);
                if (ids.length == 1) {
                    Fast.api.open($.fn.bootstrapTable.defaults.extend.refund_url + '?ids=' + ids.join(','), __('Refund'), {
                        area: ['60%', '40%']
                    });
                } else {
                    Toastr.error(__('Please choose one item'));
                }
            });
        },
        detail: function () {
            Controller.api.bindevent();
        },
        refund: function () {
            Controller.api.bindevent();
        },
        statistics: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
