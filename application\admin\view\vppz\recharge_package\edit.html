<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}" placeholder="请输入套餐名称">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-amount" class="form-control" name="row[amount]" type="number" step="0.01" min="0" value="{$row.amount}" placeholder="请输入充值金额" onchange="calculateReward()">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Reward type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-reward_type" class="form-control selectpicker" name="row[reward_type]" onchange="calculateReward()">
                {foreach name="rewardTypeList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.reward_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Reward value')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-reward_value" class="form-control" name="row[reward_value]" type="number" step="0.01" min="0" value="{$row.reward_value}" placeholder="固定金额或百分比数值" onchange="calculateReward()">
            <span class="help-block">固定金额时输入具体金额，百分比时输入百分比数值（如：10表示10%）</span>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Reward amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-reward_amount" class="form-control" name="row[reward_amount]" type="number" step="0.01" min="0" value="{$row.reward_amount}" readonly placeholder="自动计算的奖励金额">
            <span class="help-block">根据充值金额和奖励设置自动计算</span>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Description')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-description" class="form-control" rows="3" name="row[description]" placeholder="请输入套餐描述">{$row.description|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Icon')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-icon" class="form-control" size="50" name="row[icon]" type="text" value="{$row.icon|htmlentities}" placeholder="请选择套餐图标">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-icon" class="btn btn-danger plupload" data-input-id="c-icon" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-icon"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-icon" class="btn btn-primary fachoose" data-input-id="c-icon" data-preview-id="p-icon"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-icon"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-icon"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sort')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort" class="form-control" name="row[sort]" type="number" value="{$row.sort}" placeholder="排序权重，数值越大越靠前">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-status" class="form-control selectpicker" name="row[status]">
                {foreach name="statusList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.status"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is hot')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-is_hot" class="form-control selectpicker" name="row[is_hot]">
                {foreach name="isHotList" item="vo"}
                <option value="{$key}" {in name="key" value="$row.is_hot"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Start time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-start_time" class="form-control datetimepicker" name="row[start_time]" type="text" value="{$row.start_time}" placeholder="生效开始时间，留空表示立即生效">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('End time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-end_time" class="form-control datetimepicker" name="row[end_time]" type="text" value="{$row.end_time}" placeholder="生效结束时间，留空表示永久有效">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<script>
function calculateReward() {
    var amount = parseFloat($('#c-amount').val()) || 0;
    var rewardType = $('#c-reward_type').val();
    var rewardValue = parseFloat($('#c-reward_value').val()) || 0;
    
    if (amount > 0 && rewardValue > 0) {
        $.post('{:url("calculate_reward")}', {
            amount: amount,
            reward_type: rewardType,
            reward_value: rewardValue
        }, function(ret) {
            if (ret.code == 1) {
                $('#c-reward_amount').val(ret.data.reward_amount);
            }
        });
    }
}
</script>
