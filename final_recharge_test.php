<?php
// 最终充值功能测试
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>最终充值功能完整性测试</h2>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .warning{color:orange;} .link{margin:10px 0;} table{width:100%;border-collapse:collapse;} th,td{border:1px solid #ddd;padding:8px;text-align:left;}</style>\n";

try {
    // 数据库连接配置
    $dsn = "mysql:host=127.0.0.1;port=3306;dbname=fast;charset=utf8mb4";
    $username = "root";
    $password = "tHucJ7ggCMFFC72P";
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✓ 数据库连接成功</div>\n";
    
    // 测试总结
    $testResults = [];
    
    // 1. 数据库结构测试
    echo "<h3>1. 数据库结构测试</h3>\n";
    
    $tables = [
        'fa_vppz_recharge_package' => '充值套餐表',
        'fa_vppz_recharge_order' => '充值订单表'
    ];
    
    $dbStructureOk = true;
    foreach ($tables as $table => $desc) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='success'>✓ {$desc} 存在</div>\n";
                
                // 检查记录数
                $countStmt = $pdo->query("SELECT COUNT(*) as count FROM {$table}");
                $count = $countStmt->fetch();
                echo "<div class='info'>  - 记录数: {$count['count']}</div>\n";
            } else {
                echo "<div class='error'>✗ {$desc} 不存在</div>\n";
                $dbStructureOk = false;
            }
        } catch (Exception $e) {
            echo "<div class='error'>✗ 检查 {$desc} 时出错: " . $e->getMessage() . "</div>\n";
            $dbStructureOk = false;
        }
    }
    $testResults['数据库结构'] = $dbStructureOk;
    
    // 2. 用户表扩展字段测试
    echo "<h3>2. 用户表扩展字段测试</h3>\n";
    
    $userFieldsOk = true;
    $userFields = ['balance', 'total_recharge', 'total_reward'];
    foreach ($userFields as $field) {
        try {
            $stmt = $pdo->query("SHOW COLUMNS FROM fa_vppz_user LIKE '{$field}'");
            if ($stmt->rowCount() > 0) {
                echo "<div class='success'>✓ 用户表{$field}字段存在</div>\n";
            } else {
                echo "<div class='error'>✗ 用户表{$field}字段不存在</div>\n";
                $userFieldsOk = false;
            }
        } catch (Exception $e) {
            echo "<div class='error'>✗ 检查用户表{$field}字段时出错: " . $e->getMessage() . "</div>\n";
            $userFieldsOk = false;
        }
    }
    $testResults['用户表扩展'] = $userFieldsOk;
    
    // 3. 控制器文件测试
    echo "<h3>3. 控制器文件测试</h3>\n";
    
    $controllers = [
        'application/admin/controller/vppz/RechargePackage.php' => '充值套餐控制器',
        'application/admin/controller/vppz/RechargeOrder.php' => '充值订单控制器',
        'application/api/controller/vppz/Recharge.php' => '充值API控制器'
    ];
    
    $controllersOk = true;
    foreach ($controllers as $file => $desc) {
        if (file_exists($file)) {
            echo "<div class='success'>✓ {$desc} 文件存在</div>\n";
        } else {
            echo "<div class='error'>✗ {$desc} 文件不存在</div>\n";
            $controllersOk = false;
        }
    }
    $testResults['控制器文件'] = $controllersOk;
    
    // 4. 模型文件测试
    echo "<h3>4. 模型文件测试</h3>\n";
    
    $models = [
        'application/admin/model/vppz/RechargePackage.php' => '充值套餐模型',
        'application/admin/model/vppz/RechargeOrder.php' => '充值订单模型'
    ];
    
    $modelsOk = true;
    foreach ($models as $file => $desc) {
        if (file_exists($file)) {
            echo "<div class='success'>✓ {$desc} 文件存在</div>\n";
        } else {
            echo "<div class='error'>✗ {$desc} 文件不存在</div>\n";
            $modelsOk = false;
        }
    }
    $testResults['模型文件'] = $modelsOk;
    
    // 5. 视图文件测试
    echo "<h3>5. 视图文件测试</h3>\n";
    
    $views = [
        'application/admin/view/vppz/recharge_package/index.html' => '充值套餐列表视图',
        'application/admin/view/vppz/recharge_package/add.html' => '充值套餐添加视图',
        'application/admin/view/vppz/recharge_package/edit.html' => '充值套餐编辑视图',
        'application/admin/view/vppz/recharge_order/index.html' => '充值订单列表视图'
    ];
    
    $viewsOk = true;
    foreach ($views as $file => $desc) {
        if (file_exists($file)) {
            echo "<div class='success'>✓ {$desc} 文件存在</div>\n";
        } else {
            echo "<div class='error'>✗ {$desc} 文件不存在</div>\n";
            $viewsOk = false;
        }
    }
    $testResults['视图文件'] = $viewsOk;
    
    // 6. JavaScript文件测试
    echo "<h3>6. JavaScript文件测试</h3>\n";
    
    $jsFiles = [
        'public/assets/js/backend/vppz/recharge_package.js' => '充值套餐JS',
        'public/assets/js/backend/vppz/recharge_order.js' => '充值订单JS'
    ];
    
    $jsOk = true;
    foreach ($jsFiles as $file => $desc) {
        if (file_exists($file)) {
            echo "<div class='success'>✓ {$desc} 文件存在</div>\n";
        } else {
            echo "<div class='error'>✗ {$desc} 文件不存在</div>\n";
            $jsOk = false;
        }
    }
    $testResults['JavaScript文件'] = $jsOk;
    
    // 7. 语言包文件测试
    echo "<h3>7. 语言包文件测试</h3>\n";
    
    $langFiles = [
        'application/admin/lang/zh-cn/vppz/recharge_package.php' => '充值套餐语言包',
        'application/admin/lang/zh-cn/vppz/recharge_order.php' => '充值订单语言包'
    ];
    
    $langOk = true;
    foreach ($langFiles as $file => $desc) {
        if (file_exists($file)) {
            echo "<div class='success'>✓ {$desc} 文件存在</div>\n";
        } else {
            echo "<div class='error'>✗ {$desc} 文件不存在</div>\n";
            $langOk = false;
        }
    }
    $testResults['语言包文件'] = $langOk;
    
    // 8. 菜单权限测试
    echo "<h3>8. 菜单权限测试</h3>\n";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM fa_auth_rule WHERE name LIKE 'vppz/recharge%'");
    $stmt->execute();
    $menuCount = $stmt->fetch()['count'];
    
    $menuOk = $menuCount > 0;
    if ($menuOk) {
        echo "<div class='success'>✓ 找到 {$menuCount} 个充值相关菜单</div>\n";
    } else {
        echo "<div class='error'>✗ 没有找到充值相关菜单</div>\n";
    }
    $testResults['菜单权限'] = $menuOk;
    
    // 9. 支付集成测试
    echo "<h3>9. 支付集成测试</h3>\n";
    
    $payFile = 'addons/vppz/controller/Pay.php';
    $payOk = false;
    if (file_exists($payFile)) {
        $content = file_get_contents($payFile);
        if (strpos($content, 'payResultRCH') !== false && strpos($content, 'RechargeOrderModel') !== false) {
            echo "<div class='success'>✓ 支付回调扩展已完成</div>\n";
            $payOk = true;
        } else {
            echo "<div class='error'>✗ 支付回调扩展不完整</div>\n";
        }
    } else {
        echo "<div class='error'>✗ 支付控制器文件不存在</div>\n";
    }
    $testResults['支付集成'] = $payOk;
    
    // 10. 插件菜单配置测试
    echo "<h3>10. 插件菜单配置测试</h3>\n";
    
    $vppzFile = 'addons/vppz/Vppz.php';
    $vppzOk = false;
    if (file_exists($vppzFile)) {
        $content = file_get_contents($vppzFile);
        if (strpos($content, 'recharge_package') !== false && strpos($content, 'recharge_order') !== false) {
            echo "<div class='success'>✓ 插件菜单配置已更新</div>\n";
            $vppzOk = true;
        } else {
            echo "<div class='error'>✗ 插件菜单配置未更新</div>\n";
        }
    } else {
        echo "<div class='error'>✗ 插件主文件不存在</div>\n";
    }
    $testResults['插件菜单配置'] = $vppzOk;
    
    // 测试结果汇总
    echo "<h3>测试结果汇总</h3>\n";
    
    $totalTests = count($testResults);
    $passedTests = array_sum($testResults);
    $failedTests = $totalTests - $passedTests;
    
    echo "<table>\n";
    echo "<tr><th>测试项目</th><th>结果</th></tr>\n";
    foreach ($testResults as $test => $result) {
        $resultText = $result ? '<span style="color:green;">✓ 通过</span>' : '<span style="color:red;">✗ 失败</span>';
        echo "<tr><td>{$test}</td><td>{$resultText}</td></tr>\n";
    }
    echo "</table>\n";
    
    echo "<div style='margin:20px 0; padding:15px; border:1px solid #ddd; background:#f9f9f9;'>\n";
    echo "<strong>总体测试结果:</strong><br>\n";
    echo "总测试项: {$totalTests}<br>\n";
    echo "通过项: <span style='color:green;'>{$passedTests}</span><br>\n";
    echo "失败项: <span style='color:red;'>{$failedTests}</span><br>\n";
    
    if ($failedTests == 0) {
        echo "<div class='success' style='font-size:18px; margin-top:10px;'>🎉 所有测试通过！充值功能开发完成！</div>\n";
    } else {
        echo "<div class='warning' style='font-size:18px; margin-top:10px;'>⚠️ 还有 {$failedTests} 项测试未通过，请检查相关功能。</div>\n";
    }
    echo "</div>\n";
    
    // 后台访问链接
    echo "<h3>后台管理链接</h3>\n";
    
    $baseUrl = "http://127.0.0.1:888/admin";
    echo "<div class='link'><strong>后台登录:</strong> <a href='{$baseUrl}' target='_blank'>{$baseUrl}</a></div>\n";
    echo "<div class='link'><strong>充值套餐管理:</strong> <a href='{$baseUrl}/vppz/recharge_package' target='_blank'>{$baseUrl}/vppz/recharge_package</a></div>\n";
    echo "<div class='link'><strong>充值订单管理:</strong> <a href='{$baseUrl}/vppz/recharge_order' target='_blank'>{$baseUrl}/vppz/recharge_order</a></div>\n";
    
    echo "<h3>功能说明</h3>\n";
    echo "<div class='info'>\n";
    echo "<strong>已实现的功能:</strong><br>\n";
    echo "1. 充值套餐管理 - 创建、编辑、删除充值套餐，支持奖励设置<br>\n";
    echo "2. 充值订单管理 - 查看充值订单，支持退款操作<br>\n";
    echo "3. 支付集成 - 支持微信、支付宝、余额支付<br>\n";
    echo "4. API接口 - 完整的前端API接口支持<br>\n";
    echo "5. 余额系统 - 用户余额管理和资金流水记录<br>\n";
    echo "6. 奖励机制 - 支持固定金额和百分比奖励<br>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>测试失败: " . $e->getMessage() . "</div>\n";
}

echo "<br><p><em>测试时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
