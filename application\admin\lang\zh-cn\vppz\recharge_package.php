<?php

return [
    'Id'                    => 'ID',
    'App_id'               => '应用ID',
    'Title'                => '套餐名称',
    'Description'          => '套餐描述',
    'Amount'               => '充值金额',
    'Reward_type'          => '奖励类型',
    'Reward_value'         => '奖励值',
    'Reward_amount'        => '奖励金额',
    'Icon'                 => '套餐图标',
    'Sort'                 => '排序',
    'Status'               => '状态',
    'Is_hot'               => '是否热门',
    'Createtime'           => '创建时间',
    'Updatetime'           => '更新时间',
    'Fixed amount'         => '固定金额',
    'Percentage'           => '百分比',
    'Normal'               => '正常',
    'Hidden'               => '隐藏',
    'Yes'                  => '是',
    'No'                   => '否',
    'Actual amount'        => '实际到账',
    'Calculate reward'     => '计算奖励',
    'Package management'   => '套餐管理',
    'Add package'          => '添加套餐',
    'Edit package'         => '编辑套餐',
    'Delete package'       => '删除套餐',
    'Batch update'         => '批量更新',
    'Package icon'         => '套餐图标',
    'Hot package'          => '热门套餐',
    'Package status'       => '套餐状态',
    'Reward calculation'   => '奖励计算',
    'Please enter package title' => '请输入套餐名称',
    'Please enter package description' => '请输入套餐描述',
    'Please enter recharge amount' => '请输入充值金额',
    'Please select reward type' => '请选择奖励类型',
    'Please enter reward value' => '请输入奖励值',
    'Amount must be greater than 0' => '充值金额必须大于0',
    'Reward value must be greater than 0' => '奖励值必须大于0',
    'Package title already exists' => '套餐名称已存在',
    'Package created successfully' => '套餐创建成功',
    'Package updated successfully' => '套餐更新成功',
    'Package deleted successfully' => '套餐删除成功',
    'Package not found' => '套餐不存在',
    'Cannot delete package with orders' => '存在订单的套餐无法删除',
];
