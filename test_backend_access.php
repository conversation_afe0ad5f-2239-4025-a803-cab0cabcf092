<?php
// 测试后台访问
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>测试后台充值功能访问</h2>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .link{margin:10px 0;}</style>\n";

try {
    // 数据库连接配置
    $dsn = "mysql:host=127.0.0.1;port=3306;dbname=fast;charset=utf8mb4";
    $username = "root";
    $password = "tHucJ7ggCMFFC72P";
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✓ 数据库连接成功</div>\n";
    
    // 检查菜单是否存在
    echo "<h3>1. 检查菜单权限</h3>\n";
    
    $stmt = $pdo->prepare("SELECT id, name, title, icon, ismenu FROM fa_auth_rule WHERE name LIKE 'vppz/recharge%' ORDER BY name");
    $stmt->execute();
    $menus = $stmt->fetchAll();
    
    if (count($menus) > 0) {
        echo "<div class='success'>✓ 找到 " . count($menus) . " 个充值相关菜单</div>\n";
        echo "<table border='1' style='border-collapse:collapse; margin:10px 0;'>\n";
        echo "<tr><th>ID</th><th>权限名称</th><th>菜单标题</th><th>图标</th><th>是否菜单</th></tr>\n";
        foreach ($menus as $menu) {
            echo "<tr>";
            echo "<td>{$menu['id']}</td>";
            echo "<td>{$menu['name']}</td>";
            echo "<td>{$menu['title']}</td>";
            echo "<td>{$menu['icon']}</td>";
            echo "<td>" . ($menu['ismenu'] ? '是' : '否') . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<div class='error'>✗ 没有找到充值相关菜单</div>\n";
    }
    
    // 检查超级管理员权限
    echo "<h3>2. 检查超级管理员权限</h3>\n";
    
    $stmt = $pdo->prepare("SELECT rules FROM fa_auth_group WHERE id = 1");
    $stmt->execute();
    $adminGroup = $stmt->fetch();
    
    if ($adminGroup) {
        $rules = explode(',', $adminGroup['rules']);
        $rechargeMenuIds = array_column($menus, 'id');
        $hasPermission = true;
        
        foreach ($rechargeMenuIds as $menuId) {
            if (!in_array($menuId, $rules)) {
                $hasPermission = false;
                break;
            }
        }
        
        if ($hasPermission) {
            echo "<div class='success'>✓ 超级管理员拥有充值功能权限</div>\n";
        } else {
            echo "<div class='error'>✗ 超级管理员缺少充值功能权限</div>\n";
        }
    }
    
    // 生成后台访问链接
    echo "<h3>3. 后台访问链接</h3>\n";
    
    $baseUrl = "http://127.0.0.1:888/admin";
    
    echo "<div class='link'><strong>后台登录:</strong> <a href='{$baseUrl}' target='_blank'>{$baseUrl}</a></div>\n";
    echo "<div class='link'><strong>充值套餐管理:</strong> <a href='{$baseUrl}/vppz/recharge_package' target='_blank'>{$baseUrl}/vppz/recharge_package</a></div>\n";
    echo "<div class='link'><strong>充值订单管理:</strong> <a href='{$baseUrl}/vppz/recharge_order' target='_blank'>{$baseUrl}/vppz/recharge_order</a></div>\n";
    
    // 检查控制器文件
    echo "<h3>4. 检查控制器文件</h3>\n";
    
    $controllers = [
        'application/admin/controller/vppz/RechargePackage.php' => '充值套餐控制器',
        'application/admin/controller/vppz/RechargeOrder.php' => '充值订单控制器'
    ];
    
    foreach ($controllers as $file => $desc) {
        if (file_exists($file)) {
            echo "<div class='success'>✓ {$desc} 文件存在</div>\n";
            
            // 检查文件内容
            $content = file_get_contents($file);
            if (strpos($content, 'namespace app\admin\controller\vppz') !== false) {
                echo "<div class='info'>  - 命名空间正确</div>\n";
            } else {
                echo "<div class='error'>  - 命名空间错误</div>\n";
            }
            
            if (strpos($content, 'extends Base') !== false) {
                echo "<div class='info'>  - 继承Base类正确</div>\n";
            } else {
                echo "<div class='error'>  - 继承Base类错误</div>\n";
            }
        } else {
            echo "<div class='error'>✗ {$desc} 文件不存在</div>\n";
        }
    }
    
    // 检查视图文件
    echo "<h3>5. 检查视图文件</h3>\n";
    
    $views = [
        'application/admin/view/vppz/recharge_package/index.html' => '充值套餐列表视图',
        'application/admin/view/vppz/recharge_package/add.html' => '充值套餐添加视图',
        'application/admin/view/vppz/recharge_package/edit.html' => '充值套餐编辑视图',
        'application/admin/view/vppz/recharge_order/index.html' => '充值订单列表视图'
    ];
    
    foreach ($views as $file => $desc) {
        if (file_exists($file)) {
            echo "<div class='success'>✓ {$desc} 文件存在</div>\n";
        } else {
            echo "<div class='error'>✗ {$desc} 文件不存在</div>\n";
        }
    }
    
    // 检查JavaScript文件
    echo "<h3>6. 检查JavaScript文件</h3>\n";
    
    $jsFiles = [
        'public/assets/js/backend/vppz/recharge_package.js' => '充值套餐JS',
        'public/assets/js/backend/vppz/recharge_order.js' => '充值订单JS'
    ];
    
    foreach ($jsFiles as $file => $desc) {
        if (file_exists($file)) {
            echo "<div class='success'>✓ {$desc} 文件存在</div>\n";
        } else {
            echo "<div class='error'>✗ {$desc} 文件不存在</div>\n";
        }
    }
    
    echo "<h3>测试完成！</h3>\n";
    echo "<div class='info'>如果所有检查都通过，请尝试访问上面的后台链接。</div>\n";
    echo "<div class='info'>如果访问出现问题，请检查：</div>\n";
    echo "<div class='info'>1. 是否已登录后台管理系统</div>\n";
    echo "<div class='info'>2. 当前登录用户是否有相应权限</div>\n";
    echo "<div class='info'>3. 控制器和视图文件是否正确</div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>测试失败: " . $e->getMessage() . "</div>\n";
}

echo "<br><p><em>测试时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
