# 嘀嗒陪护系统 - 会员充值功能开发方案

## 1. 需求分析

### 功能要求
1. **后台管理**: 在嘀嗒陪护菜单中生成充值管理菜单
2. **支付集成**: 通过系统中的epay支付插件充值余额
3. **余额支付**: 支付费用时可选择会员余额进行支付
4. **订单管理**: 后台有菜单查看充值订单
5. **充值套餐**: 有充值套餐可供充值时选择，套餐在后台进行管理设置
6. **充值奖励**: 充值不同的套餐可获得对应的奖励，同样在后台进行设置

### 系统现状分析
- ✅ **支付系统**: 已有epay支付插件，支持微信、支付宝
- ✅ **用户系统**: 已有vppz_user表，但缺少余额字段
- ✅ **资金流水**: 已有vppz_money表记录资金变动
- ✅ **菜单系统**: 已有完整的菜单管理机制
- ⚠️ **余额字段**: 需要在用户表中添加余额相关字段
- ⚠️ **充值业务**: 需要新增充值相关的数据表和业务逻辑

## 2. 数据库设计

### 2.1 用户表扩展 (fa_vppz_user)
```sql
-- 在现有用户表中添加余额相关字段
ALTER TABLE `fa_vppz_user` 
ADD COLUMN `balance` decimal(12,2) DEFAULT '0.00' COMMENT '账户余额' AFTER `expends`,
ADD COLUMN `total_recharge` decimal(12,2) DEFAULT '0.00' COMMENT '累计充值金额' AFTER `balance`,
ADD COLUMN `total_reward` decimal(12,2) DEFAULT '0.00' COMMENT '累计充值奖励' AFTER `total_recharge`;
```

### 2.2 充值套餐表 (fa_vppz_recharge_package)
```sql
CREATE TABLE `fa_vppz_recharge_package` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_id` bigint(20) DEFAULT '0' COMMENT '应用ID',
  `area_id` bigint(20) DEFAULT '0' COMMENT '运营区域ID',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '套餐名称',
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '充值金额',
  `reward_amount` decimal(12,2) DEFAULT '0.00' COMMENT '奖励金额',
  `reward_type` enum('fixed','percent') DEFAULT 'fixed' COMMENT '奖励类型:fixed=固定金额,percent=百分比',
  `reward_value` decimal(8,2) DEFAULT '0.00' COMMENT '奖励值',
  `description` text COMMENT '套餐描述',
  `icon` varchar(255) DEFAULT '' COMMENT '套餐图标',
  `sort` int(10) DEFAULT '0' COMMENT '排序权重',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态',
  `is_hot` tinyint(1) DEFAULT '0' COMMENT '是否热门推荐',
  `start_time` bigint(20) DEFAULT '0' COMMENT '生效开始时间',
  `end_time` bigint(20) DEFAULT '0' COMMENT '生效结束时间',
  `createtime` bigint(20) DEFAULT '0' COMMENT '创建时间',
  `updatetime` bigint(20) DEFAULT '0' COMMENT '更新时间',
  `deletetime` bigint(20) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `app_area` (`app_id`,`area_id`),
  KEY `status_sort` (`status`,`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值套餐表';
```

### 2.3 充值订单表 (fa_vppz_recharge_order)
```sql
CREATE TABLE `fa_vppz_recharge_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `app_id` bigint(20) DEFAULT '0' COMMENT '应用ID',
  `area_id` bigint(20) DEFAULT '0' COMMENT '运营区域ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `package_id` bigint(20) DEFAULT '0' COMMENT '套餐ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `trade_no` varchar(64) DEFAULT '' COMMENT '第三方交易号',
  `amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '充值金额',
  `reward_amount` decimal(12,2) DEFAULT '0.00' COMMENT '奖励金额',
  `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '实际到账金额',
  `pay_type` enum('wechat','alipay','balance') DEFAULT 'wechat' COMMENT '支付方式',
  `status` enum('pending','paid','failed','refunded') DEFAULT 'pending' COMMENT '订单状态',
  `pay_time` bigint(20) DEFAULT '0' COMMENT '支付时间',
  `pay_params` text COMMENT '支付回调参数',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `createtime` bigint(20) DEFAULT '0' COMMENT '创建时间',
  `updatetime` bigint(20) DEFAULT '0' COMMENT '更新时间',
  `deletetime` bigint(20) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `user_id` (`user_id`),
  KEY `status_time` (`status`,`createtime`),
  KEY `app_area` (`app_id`,`area_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值订单表';
```

### 2.4 扩展资金流水表业务类型
```sql
-- 在现有vppz_money表的biz字段中添加充值相关类型
-- 修改biz字段枚举值，添加recharge
ALTER TABLE `fa_vppz_money` 
MODIFY COLUMN `biz` enum('order','outcash','settle','recharge') CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '业务:order=交易,outcash=提现,settle=结算,recharge=充值';
```

## 3. 开发步骤

### 第一阶段：数据库结构调整
1. **执行数据库脚本**
   - 扩展用户表添加余额字段
   - 创建充值套餐表
   - 创建充值订单表
   - 扩展资金流水表业务类型

### 第二阶段：后台管理功能开发
1. **充值套餐管理**
   - 控制器: `application/admin/controller/vppz/RechargePackage.php`
   - 模型: `application/admin/model/vppz/RechargePackage.php`
   - 视图: `application/admin/view/vppz/recharge_package/`
   - 功能: CRUD操作、状态管理、排序

2. **充值订单管理**
   - 控制器: `application/admin/controller/vppz/RechargeOrder.php`
   - 模型: `application/admin/model/vppz/RechargeOrder.php`
   - 视图: `application/admin/view/vppz/recharge_order/`
   - 功能: 订单查看、状态管理、退款处理

3. **用户余额管理**
   - 扩展现有用户管理功能
   - 添加余额查看和手动调整功能
   - 余额变动记录查看

### 第三阶段：前端充值功能开发
1. **API接口开发**
   - 控制器: `application/api/controller/vppz/Recharge.php`
   - 功能: 套餐列表、创建充值订单、支付回调处理

2. **微信小程序页面**
   - 充值套餐选择页面
   - 支付方式选择页面
   - 充值记录查看页面

### 第四阶段：支付集成
1. **扩展支付回调处理**
   - 修改 `addons/vppz/controller/Pay.php`
   - 添加充值订单支付回调处理逻辑
   - 集成epay支付插件

2. **余额支付功能**
   - 在现有订单支付中添加余额支付选项
   - 余额不足时的组合支付处理

### 第五阶段：菜单集成
1. **更新插件菜单配置**
   - 修改 `addons/vppz/Vppz.php` 中的菜单配置
   - 添加充值管理相关菜单项

## 4. 技术实现要点

### 4.1 支付集成
- 复用现有epay支付插件
- 充值订单号格式: `RCH_{order_id}`
- 支付回调中添加充值业务处理逻辑

### 4.2 余额管理
- 所有余额变动通过vppz_money表记录
- 实现余额加减的原子性操作
- 余额支付时的库存扣减机制

### 4.3 奖励机制
- 支持固定金额和百分比两种奖励方式
- 奖励金额单独记录，便于统计分析
- 奖励发放记录在资金流水表中

### 4.4 安全考虑
- 充值金额验证和限制
- 支付回调签名验证
- 余额操作的并发控制
- 订单状态的幂等性处理

## 5. 文件结构规划

```
application/admin/
├── controller/vppz/
│   ├── RechargePackage.php     # 充值套餐管理
│   └── RechargeOrder.php       # 充值订单管理
├── model/vppz/
│   ├── RechargePackage.php     # 充值套餐模型
│   └── RechargeOrder.php       # 充值订单模型
└── view/vppz/
    ├── recharge_package/       # 充值套餐视图
    └── recharge_order/         # 充值订单视图

application/api/controller/vppz/
└── Recharge.php                # 充值API接口

addons/vppz/
├── controller/
│   └── Pay.php                 # 扩展支付回调处理
└── wxapp/vp_pz/pages/
    ├── recharge/               # 充值相关小程序页面
    └── balance/                # 余额相关小程序页面
```

## 6. 开发时间估算

- **第一阶段**: 0.5天 (数据库设计和创建)
- **第二阶段**: 2天 (后台管理功能)
- **第三阶段**: 1.5天 (前端API和页面)
- **第四阶段**: 1天 (支付集成)
- **第五阶段**: 0.5天 (菜单集成)
- **测试调试**: 0.5天

**总计**: 约6天开发时间

## 7. 验收标准

1. ✅ 后台可以管理充值套餐（增删改查）
2. ✅ 后台可以查看充值订单和用户余额
3. ✅ 用户可以选择套餐进行充值
4. ✅ 支持微信、支付宝支付充值
5. ✅ 充值成功后余额正确增加
6. ✅ 奖励机制正常工作
7. ✅ 用户可以使用余额支付订单
8. ✅ 所有资金变动有完整记录
9. ✅ 菜单结构清晰，权限控制正确

---

**开发方案已确认，正在进行具体开发工作。**
