<?php

namespace app\api\controller\vppz;

use app\common\controller\Api;
use app\admin\model\vppz\RechargePackage;
use app\admin\model\vppz\RechargeOrder;
use app\admin\model\vppz\User;

/**
 * 充值接口
 */
class Recharge extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    /**
     * 获取充值套餐列表
     */
    public function packages()
    {
        $appId = $this->request->get('app_id', 0);
        $areaId = $this->request->get('area_id', 0);

        try {
            $packages = RechargePackage::getValidPackages($appId, $areaId);
            
            $result = [];
            foreach ($packages as $package) {
                $result[] = [
                    'id' => $package['id'],
                    'title' => $package['title'],
                    'amount' => $package['amount'],
                    'reward_amount' => $package['reward_amount'],
                    'total_amount' => $package['amount'] + $package['reward_amount'],
                    'description' => $package['description'],
                    'icon' => $package['icon'],
                    'is_hot' => $package['is_hot'],
                    'actual_amount_text' => $package['actual_amount_text']
                ];
            }

            $this->success('获取成功', $result);
        } catch (\Exception $e) {
            $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 创建充值订单
     */
    public function create_order()
    {
        $userId = $this->auth->id;
        $packageId = $this->request->post('package_id', 0);
        $appId = $this->request->post('app_id', 0);
        $areaId = $this->request->post('area_id', 0);

        if (!$userId) {
            $this->error('请先登录');
        }

        if (!$packageId) {
            $this->error('请选择充值套餐');
        }

        try {
            // 验证套餐有效性
            if (!RechargePackage::isValidPackage($packageId, $appId, $areaId)) {
                $this->error('充值套餐无效或已下架');
            }

            // 获取套餐信息
            $package = RechargePackage::get($packageId);
            if (!$package) {
                $this->error('充值套餐不存在');
            }

            // 获取用户信息
            $user = User::get($userId);
            if (!$user) {
                $this->error('用户不存在');
            }

            // 创建订单数据
            $orderData = [
                'app_id' => $appId,
                'area_id' => $areaId,
                'user_id' => $userId,
                'package_id' => $packageId,
                'amount' => $package['amount'],
                'reward_amount' => $package['reward_amount'],
                'pay_type' => 'wechat' // 默认微信支付
            ];

            // 创建订单
            $order = RechargeOrder::createOrder($orderData);
            if (!$order) {
                $this->error('订单创建失败');
            }

            $this->success('订单创建成功', [
                'order_id' => $order['id'],
                'order_no' => $order['order_no'],
                'amount' => $order['amount'],
                'reward_amount' => $order['reward_amount'],
                'total_amount' => $order['total_amount']
            ]);

        } catch (\Exception $e) {
            $this->error('创建订单失败：' . $e->getMessage());
        }
    }

    /**
     * 获取支付参数
     */
    public function get_pay_params()
    {
        $orderNo = $this->request->post('order_no', '');
        $payType = $this->request->post('pay_type', 'wechat');

        if (!$orderNo) {
            $this->error('订单号不能为空');
        }

        try {
            // 获取订单信息
            $order = RechargeOrder::where('order_no', $orderNo)->find();
            if (!$order) {
                $this->error('订单不存在');
            }

            if ($order['status'] != 'pending') {
                $this->error('订单状态异常');
            }

            // 更新支付方式
            $order->save(['pay_type' => $payType]);

            // 调用epay插件获取支付参数
            $epayConfig = get_addon_config('epay');
            if (!$epayConfig) {
                $this->error('支付插件未配置');
            }

            // 构建支付参数
            $payParams = [
                'order_no' => $order['order_no'],
                'amount' => $order['amount'],
                'title' => '账户充值',
                'description' => '充值' . $order['amount'] . '元',
                'notify_url' => url('vppz/pay/recharge_notify', [], true, true),
                'return_url' => url('vppz/recharge/pay_return', [], true, true),
                'pay_type' => $payType
            ];

            // 根据支付方式生成不同的支付参数
            if ($payType == 'wechat') {
                // 微信支付参数
                $payData = $this->generateWechatPayParams($payParams);
            } elseif ($payType == 'alipay') {
                // 支付宝支付参数
                $payData = $this->generateAlipayPayParams($payParams);
            } else {
                $this->error('不支持的支付方式');
            }

            $this->success('获取支付参数成功', $payData);

        } catch (\Exception $e) {
            $this->error('获取支付参数失败：' . $e->getMessage());
        }
    }

    /**
     * 支付结果回调
     */
    public function pay_return()
    {
        $orderNo = $this->request->get('order_no', '');
        
        if ($orderNo) {
            $order = RechargeOrder::where('order_no', $orderNo)->find();
            if ($order && $order['status'] == 'paid') {
                $this->success('支付成功', [
                    'order_no' => $order['order_no'],
                    'amount' => $order['amount'],
                    'reward_amount' => $order['reward_amount'],
                    'total_amount' => $order['total_amount']
                ]);
            }
        }

        $this->error('支付失败或订单状态异常');
    }

    /**
     * 获取充值记录
     */
    public function orders()
    {
        $userId = $this->auth->id;
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        if (!$userId) {
            $this->error('请先登录');
        }

        try {
            $orders = RechargeOrder::where('user_id', $userId)
                ->with(['package'])
                ->order('createtime DESC')
                ->paginate($limit, false, ['page' => $page]);

            $result = [];
            foreach ($orders as $order) {
                $result[] = [
                    'id' => $order['id'],
                    'order_no' => $order['order_no'],
                    'package_title' => $order['package']['title'] ?? '',
                    'amount' => $order['amount'],
                    'reward_amount' => $order['reward_amount'],
                    'total_amount' => $order['total_amount'],
                    'pay_type' => $order['pay_type'],
                    'pay_type_text' => $order['pay_type_text'],
                    'status' => $order['status'],
                    'status_text' => $order['status_text'],
                    'pay_time' => $order['pay_time'],
                    'pay_time_text' => $order['pay_time_text'],
                    'createtime' => $order['createtime'],
                    'createtime_text' => date('Y-m-d H:i:s', $order['createtime'])
                ];
            }

            $this->success('获取成功', [
                'list' => $result,
                'total' => $orders->total(),
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            $this->error('获取充值记录失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户余额信息
     */
    public function balance()
    {
        $userId = $this->auth->id;

        if (!$userId) {
            $this->error('请先登录');
        }

        try {
            $user = User::get($userId);
            if (!$user) {
                $this->error('用户不存在');
            }

            $this->success('获取成功', [
                'balance' => $user['balance'],
                'total_recharge' => $user['total_recharge'],
                'total_reward' => $user['total_reward']
            ]);

        } catch (\Exception $e) {
            $this->error('获取余额信息失败：' . $e->getMessage());
        }
    }

    /**
     * 生成微信支付参数
     */
    private function generateWechatPayParams($params)
    {
        // 这里应该调用epay插件的微信支付接口
        // 简化处理，返回模拟数据
        return [
            'pay_type' => 'wechat',
            'order_no' => $params['order_no'],
            'amount' => $params['amount'],
            'pay_url' => url('addons/epay/api/wechat', $params, true, true)
        ];
    }

    /**
     * 生成支付宝支付参数
     */
    private function generateAlipayPayParams($params)
    {
        // 这里应该调用epay插件的支付宝支付接口
        // 简化处理，返回模拟数据
        return [
            'pay_type' => 'alipay',
            'order_no' => $params['order_no'],
            'amount' => $params['amount'],
            'pay_url' => url('addons/epay/api/alipay', $params, true, true)
        ];
    }

    /**
     * 余额支付
     */
    public function balance_pay()
    {
        $userId = $this->auth->id;
        $orderNo = $this->request->post('order_no', '');
        $payPassword = $this->request->post('pay_password', '');

        if (!$userId) {
            $this->error('请先登录');
        }

        if (!$orderNo) {
            $this->error('订单号不能为空');
        }

        try {
            // 获取订单信息
            $order = RechargeOrder::where('order_no', $orderNo)->find();
            if (!$order) {
                $this->error('订单不存在');
            }

            if ($order['user_id'] != $userId) {
                $this->error('无权操作此订单');
            }

            if ($order['status'] != 'pending') {
                $this->error('订单状态异常');
            }

            // 检查用户余额
            if (!User::checkBalance($userId, $order['amount'])) {
                $this->error('余额不足');
            }

            // 验证支付密码（如果需要）
            // 这里可以添加支付密码验证逻辑

            // 扣减用户余额
            $result = User::deductBalance(
                $userId,
                $order['amount'],
                '充值支付：' . $order['order_no'],
                'recharge',
                $order['id']
            );

            if (!$result) {
                $this->error('余额支付失败');
            }

            // 更新订单状态为已支付
            $payResult = RechargeOrder::paySuccess($order['order_no'], 'BALANCE_' . time(), [
                'pay_type' => 'balance',
                'pay_time' => time()
            ]);

            if (!$payResult) {
                // 如果订单处理失败，需要退回余额
                User::addBalance($userId, $order['amount'], '余额支付失败退款：' . $order['order_no']);
                $this->error('订单处理失败');
            }

            $this->success('余额支付成功', [
                'order_no' => $order['order_no'],
                'amount' => $order['amount'],
                'reward_amount' => $order['reward_amount'],
                'total_amount' => $order['total_amount']
            ]);

        } catch (\Exception $e) {
            $this->error('余额支付失败：' . $e->getMessage());
        }
    }
}
