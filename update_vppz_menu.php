<?php
// 更新vppz插件菜单
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>更新vppz插件菜单</h2>\n";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";

try {
    // 数据库连接配置
    $dsn = "mysql:host=127.0.0.1;port=3306;dbname=fast;charset=utf8mb4";
    $username = "root";
    $password = "tHucJ7ggCMFFC72P";
    
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<div class='success'>✓ 数据库连接成功</div>\n";
    
    // 查找vppz主菜单ID
    $stmt = $pdo->prepare("SELECT id FROM fa_auth_rule WHERE name = 'vppz' AND ismenu = 1");
    $stmt->execute();
    $mainMenu = $stmt->fetch();
    
    if (!$mainMenu) {
        echo "<div class='error'>✗ 找不到vppz主菜单</div>\n";
        exit;
    }
    
    $mainMenuId = $mainMenu['id'];
    echo "<div class='success'>✓ 找到vppz主菜单，ID: {$mainMenuId}</div>\n";
    
    // 检查充值套餐管理菜单是否存在
    $stmt = $pdo->prepare("SELECT id FROM fa_auth_rule WHERE name = 'vppz/recharge_package' AND ismenu = 1");
    $stmt->execute();
    $rechargePackageMenu = $stmt->fetch();
    
    if (!$rechargePackageMenu) {
        echo "<div class='info'>→ 创建充值套餐管理菜单</div>\n";
        
        // 获取当前最大weigh值
        $stmt = $pdo->prepare("SELECT MAX(weigh) as max_weigh FROM fa_auth_rule WHERE pid = ?");
        $stmt->execute([$mainMenuId]);
        $maxWeigh = $stmt->fetch()['max_weigh'] ?? 0;
        
        // 插入充值套餐管理主菜单
        $stmt = $pdo->prepare("INSERT INTO fa_auth_rule (type, pid, name, title, icon, condition, remark, ismenu, createtime, updatetime, weigh, status) VALUES (1, ?, 'vppz/recharge_package', '充值套餐管理', 'fa fa-gift', '', '', 1, ?, ?, ?, 'normal')");
        $stmt->execute([$mainMenuId, time(), time(), $maxWeigh + 1]);
        $rechargePackageMenuId = $pdo->lastInsertId();
        
        echo "<div class='success'>✓ 创建充值套餐管理菜单，ID: {$rechargePackageMenuId}</div>\n";
        
        // 插入充值套餐管理子菜单
        $subMenus = [
            ['name' => 'vppz/recharge_package/index', 'title' => '查看', 'icon' => 'fa fa-circle-o'],
            ['name' => 'vppz/recharge_package/add', 'title' => '添加', 'icon' => 'fa fa-circle-o'],
            ['name' => 'vppz/recharge_package/edit', 'title' => '编辑', 'icon' => 'fa fa-circle-o'],
            ['name' => 'vppz/recharge_package/del', 'title' => '删除', 'icon' => 'fa fa-circle-o'],
            ['name' => 'vppz/recharge_package/multi', 'title' => '批量更新', 'icon' => 'fa fa-circle-o'],
            ['name' => 'vppz/recharge_package/calculate_reward', 'title' => '计算奖励', 'icon' => 'fa fa-circle-o']
        ];
        
        foreach ($subMenus as $index => $menu) {
            $stmt = $pdo->prepare("INSERT INTO fa_auth_rule (type, pid, name, title, icon, condition, remark, ismenu, createtime, updatetime, weigh, status) VALUES (1, ?, ?, ?, ?, '', '', 0, ?, ?, ?, 'normal')");
            $stmt->execute([$rechargePackageMenuId, $menu['name'], $menu['title'], $menu['icon'], time(), time(), 100 - $index]);
            echo "<div class='info'>  - 创建子菜单: {$menu['title']}</div>\n";
        }
    } else {
        echo "<div class='info'>→ 充值套餐管理菜单已存在</div>\n";
    }
    
    // 检查充值订单管理菜单是否存在
    $stmt = $pdo->prepare("SELECT id FROM fa_auth_rule WHERE name = 'vppz/recharge_order' AND ismenu = 1");
    $stmt->execute();
    $rechargeOrderMenu = $stmt->fetch();
    
    if (!$rechargeOrderMenu) {
        echo "<div class='info'>→ 创建充值订单管理菜单</div>\n";
        
        // 获取当前最大weigh值
        $stmt = $pdo->prepare("SELECT MAX(weigh) as max_weigh FROM fa_auth_rule WHERE pid = ?");
        $stmt->execute([$mainMenuId]);
        $maxWeigh = $stmt->fetch()['max_weigh'] ?? 0;
        
        // 插入充值订单管理主菜单
        $stmt = $pdo->prepare("INSERT INTO fa_auth_rule (type, pid, name, title, icon, condition, remark, ismenu, createtime, updatetime, weigh, status) VALUES (1, ?, 'vppz/recharge_order', '充值订单管理', 'fa fa-list-alt', '', '', 1, ?, ?, ?, 'normal')");
        $stmt->execute([$mainMenuId, time(), time(), $maxWeigh + 1]);
        $rechargeOrderMenuId = $pdo->lastInsertId();
        
        echo "<div class='success'>✓ 创建充值订单管理菜单，ID: {$rechargeOrderMenuId}</div>\n";
        
        // 插入充值订单管理子菜单
        $subMenus = [
            ['name' => 'vppz/recharge_order/index', 'title' => '查看', 'icon' => 'fa fa-circle-o'],
            ['name' => 'vppz/recharge_order/detail', 'title' => '详情', 'icon' => 'fa fa-circle-o'],
            ['name' => 'vppz/recharge_order/refund', 'title' => '退款', 'icon' => 'fa fa-circle-o'],
            ['name' => 'vppz/recharge_order/statistics', 'title' => '统计', 'icon' => 'fa fa-circle-o'],
            ['name' => 'vppz/recharge_order/export', 'title' => '导出', 'icon' => 'fa fa-circle-o']
        ];
        
        foreach ($subMenus as $index => $menu) {
            $stmt = $pdo->prepare("INSERT INTO fa_auth_rule (type, pid, name, title, icon, condition, remark, ismenu, createtime, updatetime, weigh, status) VALUES (1, ?, ?, ?, ?, '', '', 0, ?, ?, ?, 'normal')");
            $stmt->execute([$rechargeOrderMenuId, $menu['name'], $menu['title'], $menu['icon'], time(), time(), 100 - $index]);
            echo "<div class='info'>  - 创建子菜单: {$menu['title']}</div>\n";
        }
    } else {
        echo "<div class='info'>→ 充值订单管理菜单已存在</div>\n";
    }
    
    // 更新管理员权限
    echo "<div class='info'>→ 更新管理员权限</div>\n";
    
    // 获取所有新创建的菜单ID
    $stmt = $pdo->prepare("SELECT id FROM fa_auth_rule WHERE name LIKE 'vppz/recharge_%'");
    $stmt->execute();
    $newMenus = $stmt->fetchAll();
    
    $newMenuIds = array_column($newMenus, 'id');
    
    if (!empty($newMenuIds)) {
        // 获取超级管理员组
        $stmt = $pdo->prepare("SELECT id, rules FROM fa_auth_group WHERE id = 1");
        $stmt->execute();
        $adminGroup = $stmt->fetch();
        
        if ($adminGroup) {
            $currentRules = $adminGroup['rules'] ? explode(',', $adminGroup['rules']) : [];
            $newRules = array_unique(array_merge($currentRules, $newMenuIds));
            $newRulesStr = implode(',', $newRules);
            
            $stmt = $pdo->prepare("UPDATE fa_auth_group SET rules = ? WHERE id = 1");
            $stmt->execute([$newRulesStr]);
            
            echo "<div class='success'>✓ 已更新超级管理员权限</div>\n";
        }
    }
    
    echo "<div class='success'>✓ 菜单更新完成！</div>\n";
    echo "<div class='info'>请刷新后台页面查看新的菜单项。</div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>更新失败: " . $e->getMessage() . "</div>\n";
}

echo "<br><p><em>更新时间: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
