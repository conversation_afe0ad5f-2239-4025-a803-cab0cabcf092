define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数
            Table.api.init({
                extend: {
                    index_url: 'vppz/recharge_package/index' + location.search,
                    add_url: 'vppz/recharge_package/add',
                    edit_url: 'vppz/recharge_package/edit',
                    del_url: 'vppz/recharge_package/del',
                    multi_url: 'vppz/recharge_package/multi',
                    import_url: 'vppz/recharge_package/import',
                    table: 'vppz_recharge_package',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'sort',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: false},
                        {field: 'title', title: __('Title'), operate: 'LIKE'},
                        {field: 'amount', title: __('Amount'), operate: 'BETWEEN', sortable: true},
                        {field: 'reward_amount', title: __('Reward amount'), operate: false},
                        {field: 'actual_amount_text', title: '实际到账', operate: false},
                        {field: 'reward_type', title: __('Reward type'), searchList: {"fixed":__("Fixed amount"),"percent":__("Percentage")}, formatter: Table.api.formatter.normal},
                        {field: 'sort', title: __('Sort'), operate: false, sortable: true},
                        {field: 'status', title: __('Status'), searchList: {"normal":__("Normal"),"hidden":__("Hidden")}, formatter: function(value, row, index) {
                            return $('#statustpl').html().replace(/{{d\.status}}/g, row.status).replace(/{{d\.status_text}}/g, row.status_text);
                        }},
                        {field: 'is_hot', title: __('Is hot'), searchList: {"0":__("No"),"1":__("Yes")}, formatter: function(value, row, index) {
                            return $('#ishottpl').html().replace(/{{d\.is_hot}}/g, row.is_hot);
                        }},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
                
                // 绑定奖励计算事件
                $(document).on('change', '#c-amount, #c-reward_type, #c-reward_value', function() {
                    Controller.api.calculateReward();
                });
            },
            calculateReward: function() {
                var amount = parseFloat($('#c-amount').val()) || 0;
                var rewardType = $('#c-reward_type').val();
                var rewardValue = parseFloat($('#c-reward_value').val()) || 0;
                
                if (amount > 0 && rewardValue > 0) {
                    var rewardAmount = 0;
                    if (rewardType == 'percent') {
                        rewardAmount = Math.round(amount * rewardValue) / 100;
                    } else {
                        rewardAmount = rewardValue;
                    }
                    $('#c-reward_amount').val(rewardAmount.toFixed(2));
                }
            }
        }
    };
    return Controller;
});
