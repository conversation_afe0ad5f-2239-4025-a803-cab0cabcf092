# FastAdmin 嘀嗒陪护系统开发指南

## 快速开始

### 1. 环境验证
```bash
# 访问以下地址验证环境
http://localhost/fastadmin/public/admin  # 后台管理
http://localhost/fastadmin/public/       # 前台页面
http://localhost/fastadmin/public/api/   # API接口
```

### 2. 配置PHP命令行 (可选但推荐)
将PHP安装目录添加到系统PATH环境变量，以便使用命令行工具。

## FastAdmin核心开发方法

### 1. 一键生成CRUD
FastAdmin最强大的功能是一键生成CRUD，可以快速生成完整的管理界面。

#### 基本语法
```bash
# 生成基础CRUD
php think crud -t table_name

# 生成CRUD并自动创建菜单
php think crud -t table_name -u 1

# 生成带关联的CRUD
php think crud -t table_name -r relation_table -k foreign_key
```

#### 实际示例
```bash
# 为医院类型表生成CRUD
php think crud -t vppz_hospital_type -c vppz/hospitaltype -u 1

# 为医院表生成CRUD（带医院类型关联）
php think crud -t vppz_hospital -r vppz_hospital_type -k type_id -u 1
```

### 2. 数据表设计规范

#### 必需字段
```sql
CREATE TABLE `fa_example` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例表';
```

#### 字段命名规范
- **主键**: `id` (自增整型)
- **状态字段**: `status` (enum类型，normal/hidden)
- **排序字段**: `weigh` (整型，数值越大越靠前)
- **时间字段**: `createtime`, `updatetime`, `deletetime` (整型时间戳)
- **外键字段**: `表名_id` (如 `category_id`, `user_id`)

### 3. 控制器开发

#### 基础控制器结构
```php
<?php
namespace app\admin\controller\vppz;

use app\common\controller\Backend;

class Example extends Backend
{
    protected $model = null;
    protected $noNeedRight = ['*']; // 不需要权限验证的方法
    
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\vppz\Example;
    }
    
    // 列表页面
    public function index()
    {
        // 自定义查询条件
        if ($this->request->isAjax()) {
            // AJAX请求处理
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model->where($where)->order($sort, $order)->paginate($limit);
            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }
}
```

#### 权限控制
```php
// 在控制器中设置权限
protected $noNeedLogin = ['*'];     // 不需要登录
protected $noNeedRight = ['index']; // 不需要权限验证的方法

// 在方法中检查权限
if (!$this->auth->check('example/edit')) {
    $this->error(__('You have no permission'));
}
```

### 4. 模型开发

#### 基础模型结构
```php
<?php
namespace app\admin\model\vppz;

use think\Model;
use traits\model\SoftDelete;

class Example extends Model
{
    use SoftDelete;
    
    // 表名
    protected $name = 'vppz_example';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';
    
    // 追加属性
    protected $append = ['status_text'];
    
    // 获取器
    public function getStatusTextAttr($value, $data)
    {
        $status = $value ? $value : $data['status'];
        $list = ['normal' => '正常', 'hidden' => '隐藏'];
        return isset($list[$status]) ? $list[$status] : '';
    }
    
    // 关联模型
    public function category()
    {
        return $this->belongsTo('Category', 'category_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
```

### 5. 视图开发

#### 列表页面模板
```html
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#t-all" data-toggle="tab">{:__('All')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="t-all">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}">
                            <i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add" title="{:__('Add')}">
                            <i class="fa fa-plus"></i> {:__('Add')} </a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled" title="{:__('Delete')}">
                            <i class="fa fa-trash"></i> {:__('Delete')} </a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover" width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
```

#### 表格JavaScript配置
```javascript
define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
    var Controller = {
        index: function () {
            // 初始化表格参数
            Table.api.init({
                extend: {
                    index_url: 'vppz/example/index',
                    add_url: 'vppz/example/add',
                    edit_url: 'vppz/example/edit',
                    del_url: 'vppz/example/del',
                    table: 'vppz_example',
                }
            });

            var table = $("#table");
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name')},
                        {field: 'status', title: __('Status'), formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        }
    };
    return Controller;
});
```

### 6. API开发

#### API控制器结构
```php
<?php
namespace app\api\controller\vppz;

use app\common\controller\Api;

class Example extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    
    public function index()
    {
        $list = model('vppz/Example')->where('status', 'normal')->select();
        $this->success('获取成功', $list);
    }
    
    public function detail()
    {
        $id = $this->request->param('id');
        $info = model('vppz/Example')->get($id);
        if (!$info) {
            $this->error('记录不存在');
        }
        $this->success('获取成功', $info);
    }
}
```

### 7. 插件开发

#### 插件主类结构
```php
<?php
namespace addons\example;

use app\common\library\Menu;
use think\Addons;

class Example extends Addons
{
    public $info = [
        'name' => 'example',
        'title' => '示例插件',
        'description' => '这是一个示例插件',
        'status' => 1,
        'author' => 'FastAdmin',
        'version' => '1.0.0'
    ];

    public function install()
    {
        $menu = [
            [
                'name' => 'example',
                'title' => '示例管理',
                'icon' => 'fa fa-cog',
                'sublist' => [
                    ['name' => 'example/config', 'title' => '插件配置'],
                    ['name' => 'example/index', 'title' => '数据管理']
                ]
            ]
        ];
        Menu::create($menu);
        return true;
    }

    public function uninstall()
    {
        Menu::delete('example');
        return true;
    }
}
```

## 常用开发技巧

### 1. 数据验证
```php
// 在控制器中使用验证器
$validate = new \app\admin\validate\Example;
if (!$validate->check($params)) {
    $this->error($validate->getError());
}
```

### 2. 文件上传
```php
// 处理文件上传
$file = $this->request->file('file');
if ($file) {
    $upload = new \app\common\library\Upload();
    $attachment = $upload->upload($file);
    if ($attachment) {
        $params['image'] = $attachment->url;
    }
}
```

### 3. 缓存使用
```php
// 使用缓存
use think\Cache;

// 设置缓存
Cache::set('key', $data, 3600);

// 获取缓存
$data = Cache::get('key');

// 删除缓存
Cache::rm('key');
```

### 4. 日志记录
```php
// 记录日志
use think\Log;

Log::write('操作日志内容', 'info');
Log::error('错误信息');
```

## 调试技巧

### 1. 开启调试模式
在`.env`文件中设置：
```
[app]
debug = true
trace = true
```

### 2. 查看SQL日志
```php
// 在控制器中输出SQL
echo $this->model->getLastSql();

// 或者使用调试工具
\think\Debug::dump($this->model->getLastSql());
```

### 3. 异常处理
```php
try {
    // 业务代码
} catch (\Exception $e) {
    \think\Log::error($e->getMessage());
    $this->error('操作失败：' . $e->getMessage());
}
```

---

这个开发指南涵盖了FastAdmin的核心开发方法，可以帮助您快速上手嘀嗒陪护系统的开发工作。
